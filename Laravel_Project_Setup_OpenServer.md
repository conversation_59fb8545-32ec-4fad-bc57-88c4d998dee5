
# Запуск существующего Laravel-проекта на OpenServer

## 1. Клонируйте проект (если еще не клонирован)

```bash
git clone https://gitflic.ru/project/fnyr/web-rgtournament2.git
```

## 2. Переместите проект в директорию OpenServer

Скопируйте папку проекта в директорию, например:

```
C:\OpenServer\domains\web-rgtournament2
```

---

## 3. Настройка виртуального хоста (OpenServer)

1. Запусти **OpenServer**.
2. Перейди в **"Дополнительно → Домены"**.
3. Добавь домен, например: `web-rgtournament2` → путь к папке проекта.
4. Нажми **"Применить"** или перезапусти сервер.

> После этого можно будет открыть сайт по адресу: [http://web-rgtournament2](http://web-rgtournament2)

---

## 4. Установка зависимостей Composer

Открой терминал в корне проекта и выполни:

```bash
composer install
```

---

## 5. Настройка `.env`

Создай `.env` на основе `.env.example`:

```bash
cp .env.example .env
```

Отредактируй параметры подключения к БД:

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database
DB_USERNAME=root
DB_PASSWORD=
```

> Убедись, что такая база данных создана в phpMyAdmin или вручную в MySQL.

---

## 6. Генерация ключа приложения

```bash
php artisan key:generate
```

---

## 7. Запуск миграций

```bash
php artisan migrate
```

(или `php artisan migrate:fresh` если база пустая)



---

## 8. Готово!

Открой в браузере:

```
http://web-rgtournament2
```

---

## Примечания

- Убедись, что **PHP** в OpenServer соответствует версии Laravel (например, PHP 8.2+).
