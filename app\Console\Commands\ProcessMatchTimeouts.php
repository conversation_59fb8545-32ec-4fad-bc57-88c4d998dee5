<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\LiveMatch;
use App\Enums\MatchStatus;
use Illuminate\Support\Facades\Log;

class ProcessMatchTimeouts extends Command
{
    protected $signature = 'match:process-timeouts';
    protected $description = 'Обрабатывает таймауты матчей (подтверждение готовности и голосование по картам)';

    public function handle()
    {
        $this->info('Начинаю обработку таймаутов матчей...');
        
        // Обрабатываем таймауты подтверждения готовности
        $this->processReadyCheckTimeouts();
        
        // Обрабатываем таймауты голосования по картам
        $this->processMapVotingTimeouts();
        
        $this->info('Обработка таймаутов завершена.');
    }
    
    private function processReadyCheckTimeouts()
    {
        $readyCheckMatches = LiveMatch::where('status', MatchStatus::READY_CHECK->value)->get();
        
        foreach ($readyCheckMatches as $match) {
            // Проверяем, прошло ли 30 секунд с начала подтверждения готовности
            $readyChecks = $match->readyChecks()->orderBy('created_at', 'desc')->get();
            
            if ($readyChecks->count() > 0) {
                $lastReadyCheck = $readyChecks->first();
                $timeSinceLastCheck = now()->diffInSeconds($lastReadyCheck->created_at);
                
                if ($timeSinceLastCheck >= $match->ready_check_timeout) {
                    $this->warn("Таймаут подтверждения готовности для матча {$match->id}");
                    $match->handleReadyCheckTimeout();
                }
            }
        }
    }
    
    private function processMapVotingTimeouts()
    {
        $mapVotingMatches = LiveMatch::where('status', MatchStatus::MAP_VOTING->value)->get();
        
        foreach ($mapVotingMatches as $match) {
            // Получаем последний бан карты
            $lastBan = \Illuminate\Support\Facades\DB::table('live_match_banned_maps')
                ->where('match_id', $match->id)
                ->orderBy('created_at', 'desc')
                ->first();
            
            if ($lastBan) {
                $timeSinceLastBan = now()->diffInSeconds($lastBan->created_at);
                
                if ($timeSinceLastBan >= $match->ban_timeout) {
                    $this->warn("Таймаут голосования по картам для матча {$match->id}");
                    
                    // Автоматически баним случайную карту
                    $match->autoBanMap();
                    
                    // Проверяем, осталась ли только одна карта
                    if ($match->hasOnlyOneMapLeft()) {
                        $this->info("Выбирается финальная карта для матча {$match->id}");
                        $match->selectFinalMap();
                    }
                }
            }
        }
    }
} 