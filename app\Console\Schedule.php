<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;

class Schedule
{
    /**
     * Define the application's command schedule.
     */
    public function __invoke(Schedule $schedule): void
    {
        // Запуск команды обработки матчей каждые 5 минут
        $schedule->command('matches:process')
                 ->everyFiveMinutes()
                 ->withoutOverlapping()
                 ->appendOutputTo(storage_path('logs/matches-process.log'));
                 
        // Обработка таймаутов матчей каждые 10 секунд
        $schedule->command('match:process-timeouts')
                 ->everyTenSeconds()
                 ->withoutOverlapping()
                 ->appendOutputTo(storage_path('logs/match-timeouts.log'));
                 
        // Проверка и создание нового сезона в первый день каждого месяца
        $schedule->command('seasons:manage end')
                 ->monthly()
                 ->appendOutputTo(storage_path('logs/seasons.log'));
                 
        $schedule->command('seasons:manage create')
                 ->monthly()
                 ->after(function () {
                     // Выполняется сразу после завершения предыдущей команды
                 })
                 ->appendOutputTo(storage_path('logs/seasons.log'));
    }
}
