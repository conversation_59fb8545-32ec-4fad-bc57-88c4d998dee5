<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MapBanned implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public int $matchId,
        public int $mapId,
        public int $fromCaptainId,
        public int $toCaptainId,
        public int $nextVotingTeamId
    ) {}

    public function broadcastOn()
    {
        return new PrivateChannel('captain.' . $this->toCaptainId);
    }

    public function broadcastAs()
    {
        return 'map.banned';
    }
}