<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log; // Добавлено

class AuthenticateByToken
{
    public function handle(Request $request, Closure $next)
    {
        $clubIdFromRequest = $request->query('club');
        $authTokenFromRequest = $request->query('auth_token');

        Log::info('AuthenticateByToken: Attempting auth.', ['club_id' => $clubIdFromRequest, 'auth_token' => $authTokenFromRequest]);

        if ($clubIdFromRequest && $authTokenFromRequest) {
            $user = User::where('auth_token', $authTokenFromRequest)->first();

            if ($user) {
                Log::info('AuthenticateByToken: User found.', ['user_id' => $user->id, 'user_club_id' => $user->club_id, 'token_expiry' => $user->auth_token_from_date]);

                if ($user->club_id != $clubIdFromRequest) {
                    Log::warning('AuthenticateByToken: Club ID mismatch.', ['request_club_id' => $clubIdFromRequest, 'user_club_id' => $user->club_id]);
                    return $next($request); // Важно: если клуб не совпадает, просто пропускаем дальше без авторизации
                }

                if ($user->auth_token_from_date) {
                    $expiryDate = Carbon::parse($user->auth_token_from_date);
                    Log::info('AuthenticateByToken: Token expiry date check.', ['expiry_date' => $expiryDate->toIso8601String(), 'is_future' => $expiryDate->isFuture()]);

                    if ($expiryDate->isFuture()) {
                        // Получаем полную модель пользователя, чтобы убедиться, что все отношения и т.д. загружены корректно для Auth::login()
                        // Хотя в данном случае $user уже является моделью, но для ясности можно оставить find(), если есть сомнения.
                        // $userModel = User::find($user->id); // Можно использовать просто $user, если $user->first() возвращает полную модель
                        
                        // Auth::login($user) должен работать, если $user это экземпляр User
                        Auth::login($user);
                        Log::info('AuthenticateByToken: User logged in.', ['user_id' => $user->id]);
                        // После успешной аутентификации, Laravel обычно редиректит. 
                        // Если ваш маршрут web.php настроен на редирект после middleware 'auth.token', то это произойдет автоматически.
                    } else {
                        Log::warning('AuthenticateByToken: Token expired.', ['user_id' => $user->id, 'expiry_date' => $user->auth_token_from_date]);
                    }
                } else {
                    Log::warning('AuthenticateByToken: Token expiry date not set.', ['user_id' => $user->id]);
                }
            } else {
                Log::warning('AuthenticateByToken: User not found by token.', ['auth_token' => $authTokenFromRequest]);
            }
        }

        return $next($request);
    }
}



