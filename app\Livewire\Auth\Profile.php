<?php

namespace App\Livewire\Auth;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithFileUploads;

class Profile extends Component
{
    use WithFileUploads;
    
    public $user;
    public $avatar;
    public $editMode = false;
    public $name;
    public $email;
    public $current_password;
    public $password;
    public $password_confirmation;
    
    public function mount()
    {
        $this->user = Auth::user();
        // Используем client_nick вместо name, если name не существует
        $this->name = $this->user->client_nick ?? $this->user->name ?? '';
        $this->email = $this->user->email;
    }

    public function logout()
    {
        Auth::logout();
        request()->session()->invalidate();
        request()->session()->regenerateToken();
        
        return redirect()->route('home');
    }
    
    public function toggleEditMode()
    {
        $this->editMode = !$this->editMode;
        if (!$this->editMode) {
            // Сбрасываем значения при выходе из режима редактирования
            $this->name = $this->user->client_nick ?? $this->user->name ?? '';
            $this->email = $this->user->email;
            $this->resetPasswordFields();
        }
    }
    
    public function resetPasswordFields()
    {
        $this->current_password = null;
        $this->password = null;
        $this->password_confirmation = null;
        $this->resetValidation(['current_password', 'password', 'password_confirmation']);
    }
    
    public function saveProfile()
    {
        $validationRules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:players,email,' . $this->user->id,
        ];
        
        try {
            // Если пользователь пытается изменить пароль
            if ($this->current_password || $this->password || $this->password_confirmation) {
                $validationRules['current_password'] = 'required|current_password';
                $validationRules['password'] = 'required|min:8|confirmed';
            }
            
            $this->validate($validationRules);
            
            // Определяем, какое поле использовать для имени
            if (isset($this->user->client_nick)) {
                $this->user->client_nick = $this->name;
            } else {
                $this->user->name = $this->name;
            }
            
            $this->user->email = $this->email;
            
            // Обновляем пароль, если он был изменен
            if ($this->password) {
                $this->user->password = Hash::make($this->password);
                
                // Если есть поле client_password, обновляем и его
                if (isset($this->user->client_password)) {
                    $this->user->client_password = $this->password;
                }
            }
            
            $this->user->save();
            session()->flash('success', 'Профиль успешно обновлен!');

        } catch (\Throwable $th) {
            session()->flash('error', 'Что-то пошло не так! ' . $th->getMessage());
        }
        
        $this->resetPasswordFields();
        $this->editMode = false;
    }
    
    public function updatedAvatar()
    {        
        try {
            // Валидация файла
            $validator = Validator::make(
                ['avatar' => $this->avatar],
                ['avatar' => 'required|image|max:1024']
            );            

            if ($validator->fails()) {
                throw new \Exception('Ошибка валидации: ' . implode(', ', $validator->errors()->all()));
            }

            // Создаем директорию, если её нет
            $publicPath = public_path('images/avatars');
            
            if (!file_exists($publicPath)) {
                if (!mkdir($publicPath, 0777, true)) {
                    throw new \Exception('Не удалось создать директорию для аватаров');
                }
            }

            // Проверяем права на запись
            if (!is_writable($publicPath)) {
                throw new \Exception('Нет прав на запись в директорию аватаров');
            }

            // Генерируем уникальное имя файла с именем пользователя
            $userName = str_replace(' ', '_', $this->user->client_nick ?? $this->user->name ?? 'user');
            $fileName = 'avatar_' . $userName . '_' . $this->user->id . '_' . time() . '.' . $this->avatar->getClientOriginalExtension();
            
            // Получаем старый аватар
            $oldAvatar = $this->user->avatar;
            
            // Сохраняем файл напрямую в public/images/avatars
            $tempPath = $this->avatar->getRealPath();
            $destinationPath = $publicPath . '/' . $fileName;
            
            if (!copy($tempPath, $destinationPath)) {
                throw new \Exception('Не удалось сохранить файл');
            }
            
            // Обновляем аватар пользователя
            $this->user->avatar = $fileName;
            $this->user->save();
            
            // Удаляем старый аватар, если он существует
            if ($oldAvatar && file_exists($publicPath . '/' . $oldAvatar)) {
                unlink($publicPath . '/' . $oldAvatar);
            }

            session()->flash('success', 'Аватар успешно обновлен!');

        } catch (\Throwable $th) {
            session()->flash('error', 'Ошибка при загрузке аватара: ' . $th->getMessage());
        }
        
        $this->avatar = null;        
    }

    public function deleteAvatar()
    {
        try {
            // Получаем текущий аватар
            $currentAvatar = $this->user->avatar;
            
            if ($currentAvatar) {
                // Удаляем файл аватара
                $avatarPath = public_path('images/avatars/' . $currentAvatar);
                if (file_exists($avatarPath)) {
                    unlink($avatarPath);
                }
                
                // Очищаем поле аватара в базе данных
                $this->user->avatar = null;
                $this->user->save();
                
                session()->flash('success', 'Аватар успешно удален!');
            } else {
                session()->flash('info', 'У вас нет установленного аватара');
            }
        } catch (\Throwable $th) {
            session()->flash('error', 'Ошибка при удалении аватара: ' . $th->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.auth.profile');
    }
}

