<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\User;
// use App\Models\GameMatch; // Удаляем
// use App\Models\Cs2Map; // Удаляем
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

#[Title('Создание команды')]
class CreateTeam extends Component
{
    public $userTeams = [];
    public $selectedGameId = null;
    public $teamMembers = [];
    public $teamStats = [];
    public $pendingInvitations = [];
    public $pendingRequests = [];
    public $gameName = '';

    public $receivedInvitations = [];
    
    // Удаляем свойства, связанные с созданием матча:
    // public $showCreateMatchForm = false;
    // public $selectedTeamId = null;
    // public $bannedMaps = [];
    // public $maxBans = 6;
    // public $newMatch = [
    //     'game_type' => 'cs2',
    //     'session_id' => '',
    //     'match_id' => ''
    // ];
    // public $availableMaps = []; // Удаляем

    /**
     * Загрузить список приглашений, полученных пользователем
     */
    public function loadReceivedInvitations()
    {
        if (Auth::check()) {
            $this->receivedInvitations = \App\Models\TeamInvitation::where('player_id', Auth::id())
                ->where('status', 'pending')
                ->with(['team', 'team.captain'])
                ->get();
        } else {
            $this->receivedInvitations = [];
        }
    }

    public $isLoading = false;
    
    #[On('gameChanged')]
    public function updateSelectedGame($gameId)
    {
        $this->isLoading = true; // Устанавливаем флаг загрузки
        $this->selectedGameId = $gameId;
        $this->loadUserTeamsForGame();
        $this->loadGameName();
        $this->isLoading = false; // Сбрасываем флаг загрузки
    }
    
    /**
     * Хук жизненного цикла Livewire, вызывается при монтировании компонента
     */
    public function mount($defaultGameId = 1)
    {
        $this->selectedGameId = session('selectedGameId', $defaultGameId);
        
        if ($this->selectedGameId) {
            $this->loadUserTeamsForGame();
            $this->loadGameName();
        }
        
        // Удаляем загрузку забаненных карт из сессии, теперь это в CreateMatchModal
        // $this->bannedMaps = session()->get('banned_maps', []);
        
        // Загружаем полученные приглашения
        $this->loadReceivedInvitations();
        
        $this->dispatch('teamComponentMounted');
    }
    
    public function loadUserTeamsForGame()
    {
        if (!$this->selectedGameId) {
            $this->userTeams = [];
            $this->teamMembers = [];
            $this->teamStats = [];
            $this->pendingInvitations = [];
            $this->pendingRequests = [];
            return;
        }
        
        $userId = Auth::id();
        $cacheKey = "user_teams_{$userId}_{$this->selectedGameId}";
        
        // Кэшируем данные команд на 5 минут
        $this->userTeams = Cache::remember($cacheKey, 300, function() use ($userId) {
            return \App\Models\Team::where('game_id', $this->selectedGameId)
                ->where(function($query) use ($userId) {
                    $query->whereHas('members', function($q) use ($userId) {
                        $q->where('player_id', $userId);
                    })
                    ->orWhere('captain_id', $userId);
                })
                ->with([
                    'captain:id,client_nick,avatar',
                    'game:id,name,team_size',
                    'members.user:id,client_nick,avatar',
                    'invitations' => function($query) {
                        $query->where('status', 'pending')
                            ->with('user:id,client_nick,avatar');
                    },
                    'requests' => function($query) {
                        $query->where('status', 'pending')
                            ->with('user:id,client_nick,avatar');
                    }
                ])
                ->withCount([
                    'matches',
                    'matches as wins_count' => function($query) {
                        $query->where('victory', true);
                    },
                    'matches as losses_count' => function($query) {
                        $query->where('victory', false);
                    }
                ])
                ->get();
        });
        
        // Подготавливаем данные для шаблона
        foreach ($this->userTeams as $team) {
            $this->teamMembers[$team->id] = $team->members;
            $this->pendingInvitations[$team->id] = $team->invitations;
            $this->pendingRequests[$team->id] = $team->requests;
            
            $this->teamStats[$team->id] = [
                'matches' => $team->matches_count,
                'wins' => $team->wins_count,
                'losses' => $team->losses_count,
                'draws' => 0
            ];
        }
    }
    
    /**
     * Найти команду для выбранной игры
     */
    public function findTeam()
    {
        if (!$this->selectedGameId) {
            session()->flash('error', 'Сначала выберите игру');
            return;
        }
        
        $user = Auth::user();
        
        // Проверяем, есть ли уже команда у пользователя для этой игры
        if ($user->hasTeamForGame($this->selectedGameId)) {
            session()->flash('error', 'Вы уже состоите в команде для этой игры');
            return;
        }
        
        // Сохраняем выбранную игру в сессии
        session(['selectedGameId' => $this->selectedGameId]);
        
        // Перенаправляем на страницу поиска команды с передачей выбранной игры
        return $this->redirect(route('find.team', ['gameId' => $this->selectedGameId]), navigate: true);
    }
    
    /**
     * Расформировать команду
     */
    public function disbandTeam($teamId)
    {
        $user = Auth::user();
        
        // Проверяем, является ли пользователь капитаном команды
        if (!$user->isCaptainOf($teamId)) {
            session()->flash('error', 'Только капитан может расформировать команду');
            return;
        }
        
        try {
            DB::beginTransaction();
            
            // Получаем команду
            $team = Team::find($teamId);
            if (!$team) {
                session()->flash('error', 'Команда не найдена');
                return;
            }
            
            $gameId = $team->game_id;
            
            // Удаляем все заявки на вступление
            \App\Models\JoinRequest::where('team_id', $teamId)->delete();
            
            // Удаляем все приглашения команды
            \App\Models\TeamInvitation::where('team_id', $teamId)->delete();
            
            // Удаляем всех участников команды
            TeamMember::where('team_id', $teamId)->delete();
            
            // Удаляем саму команду
            $team->delete();
            
            DB::commit();
            
            // Очищаем кэш
            Cache::forget("user_teams_{$user->id}_{$gameId}");
            Cache::forget("team_stats_{$teamId}");
            
            // Обновляем список команд пользователя
            $this->loadUserTeamsForGame();
            
            session()->flash('success', 'Команда успешно расформирована');
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при расформировании команды: ' . $e->getMessage());
            session()->flash('error', 'Произошла ошибка при расформировании команды: ' . $e->getMessage());
        }
    }

    /**
     * Пригласить игрока в команду
     */
    public function invitePlayer($teamId)
    {
        $user = Auth::user();
        
        // Проверяем, является ли пользователь капитаном команды
        if (!$user->isCaptainOf($teamId)) {
            session()->flash('error', 'Только капитан может приглашать игроков');
            return;
        }
        
        // Отправляем событие для открытия модального окна через Alpine.js
        $this->dispatch('open-invite-player-modal', ['teamId' => $teamId]);
    }

    /**
     * Загрузить список ожидающих приглашений в команду
     */
    #[On('accepted_invitation')]
    #[On('invitationSent')] // Добавляем слушателя для события из InvitePlayerModal
    public function loadPendingInvitations($teamId = null)
    {
        if (Auth::check()) {
            $this->pendingInvitations = \App\Models\TeamInvitation::where('team_id', $teamId)
                ->where('status', 'pending')
                ->with(['user', 'inviter'])
                ->get();
        }
    }

    /**
     * Загрузить список заявок на вступление в команду
     */
    public function loadPendingRequests($teamId)
    {
        // Загружаем только если пользователь капитан
        if (Auth::user()->isCaptainOf($teamId)) {
            $this->pendingRequests[$teamId] = \App\Models\JoinRequest::where('team_id', $teamId)
                ->where('status', 'pending')
                ->with('user')
                ->get();
        } else {
            $this->pendingRequests[$teamId] = [];
        }
    }

    /**
     * Принять заявку на вступление в команду
     */
    public function acceptRequest($requestId)
    {
        $request = \App\Models\JoinRequest::find($requestId);
        
        if (!$request) {
            session()->flash('error', 'Заявка не найдена');
            return;
        }
        
        // Проверяем, является ли пользователь капитаном команды
        if (!Auth::user()->isCaptainOf($request->team_id)) {
            session()->flash('error', 'Только капитан может принимать заявки');
            return;
        }
        
        // Обновляем статус заявки
        $request->status = 'accepted';
        $request->save();
        
        // Добавляем пользователя в команду
        \App\Models\TeamMember::create([
            'team_id' => $request->team_id,
            'player_id' => $request->player_id,
            'role' => 'member'
        ]);
        
        // Перезагружаем данные
        $this->loadTeamMembers($request->team_id);
        $this->loadPendingRequests($request->team_id);
        
        session()->flash('success', 'Заявка принята');
    }

    /**
     * Отклонить заявку на вступление в команду
     */
    public function rejectRequest($requestId)
    {
        $request = \App\Models\JoinRequest::find($requestId);
        
        if (!$request) {
            session()->flash('error', 'Заявка не найдена');
            return;
        }
        
        // Проверяем, является ли пользователь капитаном команды
        if (!Auth::user()->isCaptainOf($request->team_id)) {
            session()->flash('error', 'Только капитан может отклонять заявки');
            return;
        }
        
        // Обновляем статус заявки
        $request->status = 'rejected';
        $request->save();
        
        // Перезагружаем данные
        $this->loadPendingRequests($request->team_id);
        
        session()->flash('success', 'Заявка отклонена');
    }

    /**
     * Отменить приглашение игрока
     */
    public function cancelInvitation($invitationId)
    {
        $invitation = \App\Models\TeamInvitation::find($invitationId);
        
        if (!$invitation) {
            session()->flash('error', 'Приглашение не найдено');
            return;
        }
        
        // Проверяем, является ли пользователь капитаном команды
        if (!Auth::user()->isCaptainOf($invitation->team_id)) {
            session()->flash('error', 'Только капитан может отменять приглашения');
            return;
        }
        
        // Удаляем приглашение
        $invitation->delete();
        
        // Перезагружаем данные
        $this->loadPendingInvitations($invitation->team_id);
        
        session()->flash('success', 'Приглашение отменено');
    }

    /**
     * Обработчик события принятия приглашения
     */
    #[On('accepted_invitation')]
    #[On('teamCreated')] // Слушаем событие о создании команды из CreateTeamModal
    #[On('invitationSent')] // Слушаем событие об отправке приглашения из InvitePlayerModal
    #[On('matchCreated')] // Слушаем событие о создании матча из CreateMatchModal
    public function handleTeamUpdate($data = null) // Переименовал, чтобы отразить общую логику
    {
        $teamId = is_array($data) ? ($data['teamId'] ?? null) : $data;
        
        // Перезагружаем команды пользователя для выбранной игры
        $this->loadUserTeamsForGame();
        
        // Если передан конкретный teamId, обновляем только его
        if ($teamId) {
            $this->loadTeamMembers($teamId);
            $this->loadPendingInvitations($teamId);
            $this->loadPendingRequests($teamId);
        } else {
            // Иначе обновляем все команды
            foreach ($this->userTeams as $team) {
                $this->loadTeamMembers($team->id);
                $this->loadPendingInvitations($team->id);
                $this->loadPendingRequests($team->id);
            }
        }
    }
    
    // Также можно добавить обработчик для принятых приглашений (если пользователь принимает приглашение)
    public function acceptInvitation($invitationId)
    {
        $invitation = \App\Models\TeamInvitation::find($invitationId);
        
        if (!$invitation) {
            session()->flash('error', 'Приглашение не найдено');
            return;
        }
        
        // Проверяем, что текущий пользователь - это тот, кого пригласили
        if ($invitation->player_id !== Auth::id()) {
            session()->flash('error', 'У вас нет прав на это действие.');
            return;
        }

        // Проверяем, не состоит ли игрок уже в команде для этой игры
        $team = Team::find($invitation->team_id);
        if (!$team) {
            session()->flash('error', 'Команда не найдена.');
            return;
        }
        if (Auth::user()->hasTeamForGame($team->game_id)) {
            session()->flash('error', 'Вы уже состоите в команде для этой игры.');
            return;
        }

        try {
            DB::beginTransaction();

            // Добавляем пользователя в команду
            TeamMember::create([
                'team_id' => $invitation->team_id,
                'player_id' => $invitation->player_id,
                'role' => 'member'
            ]);

            // Обновляем статус приглашения на "accepted"
            $invitation->status = 'accepted';
            $invitation->save();
            
            // Удаляем все остальные ожидающие приглашения для этого игрока в этой игре
            \App\Models\TeamInvitation::where('player_id', Auth::id())
                ->where('status', 'pending')
                ->whereHas('team', function($query) use ($team) {
                    $query->where('game_id', $team->game_id);
                })
                ->delete();

            // Отклоняем все ожидающие заявки от этого игрока в другие команды в этой игре
            \App\Models\JoinRequest::where('player_id', Auth::id())
                ->where('status', 'pending')
                ->whereHas('team', function($query) use ($team) {
                    $query->where('game_id', $team->game_id);
                })
                ->update(['status' => 'rejected']);

            DB::commit();

            // Очищаем кэш для команд пользователя
            Cache::forget("user_teams_{$invitation->player_id}_{$team->game_id}");

            // Обновляем списки
            $this->loadReceivedInvitations();
            $this->loadUserTeamsForGame();

            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Приглашение принято. Вы вступили в команду!'
            ]);
            
            // Оповещаем команду, что новый участник присоединился
            event(new \App\Events\TeamMemberJoined($invitation->team_id));

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при принятии приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Ошибка при принятии приглашения: ' . $e->getMessage()
            ]);
        }
    }

    public function declineInvitation($invitationId)
    {
        $invitation = \App\Models\TeamInvitation::find($invitationId);
        
        if (!$invitation) {
            session()->flash('error', 'Приглашение не найдено');
            return;
        }
        
        if ($invitation->player_id !== Auth::id()) {
            session()->flash('error', 'У вас нет прав на это действие.');
            return;
        }

        $invitation->status = 'rejected';
        $invitation->save();

        $this->loadReceivedInvitations();
        $this->loadUserTeamsForGame(); // Обновляем на всякий случай

        $this->dispatch('showNotification', [
            'type' => 'success',
            'message' => 'Приглашение отклонено.'
        ]);
    }

    /**
     * Метод для настройки прослушивания каналов
     */
    public function getListeners()
    {
        $listeners = [
            'gameSelected' => 'handleGameSelected', // Убедитесь, что 'handleGameSelected' существует, если нет, то удалите
            'teamCreated' => 'handleTeamUpdate', // Слушаем событие о создании команды
            'invitationSent' => 'handleTeamUpdate', // Слушаем событие об отправке приглашения
            'accepted_invitation' => 'handleTeamUpdate', // Слушаем событие о принятии приглашения
            'matchCreated' => 'handleTeamUpdate', // Добавляем слушатель для события о создании матча
        ];
        
        // Добавляем слушателей для команд пользователя
        if (isset($this->userTeams) && count($this->userTeams) > 0) {
            foreach ($this->userTeams as $team) {
                $listeners['echo:team.' . $team->id . ',team.member.joined'] = 'handleTeamMemberJoined';
                $listeners['echo:team.' . $team->id . ',team.member.left'] = 'handleTeamMemberLeft';
            }
        }
        
        return $listeners;
    }

    /**
     * Обработчик события присоединения нового участника к команде
     */
    public function handleTeamMemberJoined($event)
    {
        $this->loadTeamMembers($event['teamId']);
        $this->loadUserTeamsForGame(); // Обновить весь список команд
    }

    /**
     * Обработчик события выхода участника из команды
     */
    public function handleTeamMemberLeft($event)
    {
        $this->loadTeamMembers($event['teamId']);
        $this->loadUserTeamsForGame(); // Обновить весь список команд
    }

    /**
     * Исключить участника из команды
     */
    public function kickMember($teamId, $userId)
    {
        $user = Auth::user();
        
        // Проверяем, является ли пользователь капитаном команды
        if (!$user->isCaptainOf($teamId)) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Только капитан может исключать участников'
            ]);
            return;
        }
        
        // Проверяем, не пытается ли капитан исключить сам себя
        if ($userId == $user->id) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Вы не можете исключить себя из команды'
            ]);
            return;
        }
        
        try {
            DB::beginTransaction();
            
            // Находим участника команды
            $teamMember = TeamMember::where('team_id', $teamId)
                ->where('player_id', $userId)
                ->first();
            
            if (!$teamMember) {
                $this->dispatch('showNotification', [
                    'type' => 'error',
                    'message' => 'Участник не найден'
                ]);
                return;
            }
            
            // Удаляем участника из команды
            $teamMember->delete();
            
            // Удаляем все принятые приглашения для этого участника и команды (если такие были)
            \App\Models\TeamInvitation::where('team_id', $teamId)
                ->where('player_id', $userId)
                ->where('status', 'accepted')
                ->delete();
            
            // Очищаем кэш
            $team = Team::find($teamId);
            Cache::forget("user_teams_{$user->id}_{$team->game_id}");
            Cache::forget("team_stats_{$teamId}");
            
            // Обновляем список участников команды
            $this->loadTeamMembers($teamId);
            $this->loadUserTeamsForGame();
            
            DB::commit();
            
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Участник успешно исключен из команды'
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при исключении участника: ' . $e->getMessage());
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Произошла ошибка при исключении участника'
            ]);
        }
    }

    /**
     * Получить требуемый размер команды для игры
     */
    protected function getRequiredTeamSize($gameId)
    {
        return match ((int)$gameId) {
            3 => 4, // PUBG
            default => 5, // CS2 и Dota2
        };
    }

    /**
     * Найти соперника для команды
     */
    public function findOpponent($teamId)
    {
        $user = Auth::user();
        
        // Проверяем, является ли пользователь капитаном команды
        if (!$user->isCaptainOf($teamId)) {
            session()->flash('error', 'Только капитан может искать соперника');
            return;
        }
        
        // Получаем команду
        $team = \App\Models\Team::find($teamId);
        if (!$team) {
            session()->flash('error', 'Команда не найдена');
            return;
        }
        
        // Проверяем количество участников команды
        $requiredSize = $this->getRequiredTeamSize($team->game_id);
        $teamMembersCount = count($this->teamMembers[$teamId]);
        
        if ($teamMembersCount < $requiredSize) {
            session()->flash('error', "Для поиска соперника необходимо минимум {$requiredSize} участников в команде");
            return;
        }
        
        // Перенаправляем на страницу поиска матча
        return $this->redirect(route('find.match', ['teamId' => $teamId]), navigate: true);
    }

    protected function loadGameName()
    {
        $cacheKey = "game_name_{$this->selectedGameId}";
        $this->gameName = Cache::remember($cacheKey, 3600, function() {
            return \App\Models\Game::find($this->selectedGameId)->name;
        });
    }
    
    /**
     * Создать новый матч для команды (вызывается из кнопки)
     */
    public function createNewMatch($teamId)
    {
        $user = Auth::user();
        
        // Проверяем, является ли пользователь капитаном команды
        if (!$user->isCaptainOf($teamId)) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Только капитан может создавать новые матчи'
            ]);
            return;
        }
        
        // Отправляем событие для открытия модального окна CreateMatchModal
        $this->dispatch('open-create-match-modal', ['teamId' => $teamId]);
    }
    
    /**
     * Найти новый матч для команды
     */
    public function findNewMatch($teamId)
    {
        $user = Auth::user();
        
        // Проверяем, является ли пользователь капитаном команды
        if (!$user->isCaptainOf($teamId)) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Только капитан может искать новые матчи'
            ]);
            return;
        }
        
        // Получаем команду
        $team = \App\Models\Team::find($teamId);
        if (!$team) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => 'Команда не найдена'
            ]);
            return;
        }
        
        // Проверяем количество участников команды
        $requiredSize = $this->getRequiredTeamSize($team->game_id);
        $teamMembersCount = count($this->teamMembers[$teamId] ?? []);
        
        if ($teamMembersCount < $requiredSize) {
            $this->dispatch('showNotification', [
                'type' => 'error',
                'message' => "Для поиска матча необходимо минимум {$requiredSize} участников в команде"
            ]);
            return;
        }
        
        // Сохраняем ID команды в сессии для использования на странице поиска матча
        session(['searching_team_id' => $teamId]);
        
        // Перенаправляем на страницу поиска матча
        return $this->redirect(route('find.match', ['teamId' => $teamId]), navigate: true);
    }
    
    /**
     * Загрузить список участников команды
     */
    public function loadTeamMembers($teamId)
    {
        $team = \App\Models\Team::where('id', $teamId)->with('members.user')->first();

        if ($team) {
            $this->teamMembers[$teamId] = $team->members;
        }
    }
    
    public function render()
    {
        return view('livewire.create-team', [
            'hasTeamInGame' => count($this->userTeams) > 0,
            // 'availableMaps' => $this->availableMaps ?? [] // Удаляем, теперь это в CreateMatchModal
        ]);
    }
}
