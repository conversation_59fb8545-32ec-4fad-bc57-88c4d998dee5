<?php

namespace App\Livewire;

use App\Models\Team;
use Livewire\Component;
use Livewire\Attributes\Title;

#[Title('Поиск матча')]
class FindMatch extends Component
{
    public $teamId;
    public $team;
    public $searchStatus = 'waiting';
    public $foundMatch = null;

    public function mount($teamId = null)
    {
        // Если teamId не передан, берем из сессии или редиректим на страницу выбора команды
        if (!$teamId) {
            $teamId = session('searching_team_id');
            if (!$teamId) {
                session()->flash('error', 'Не выбрана команда для поиска матча');
                return redirect()->route('create.team');
            }
        } else {
            // Сохраняем teamId в сессии
            session(['searching_team_id' => $teamId]);
        }

        $this->teamId = $teamId;

        // Загружаем команду
        $this->team = Team::with(['members.user', 'game'])->find($teamId);
        if (!$this->team) {
            session()->flash('error', 'Команда не найдена');
            return redirect()->route('create.team');
        }

        // Проверяем, есть ли активный матч для этой команды в сессии
        // $activeMatchId = session('active_match_id');
        // if ($activeMatchId) {
        //     $activeMatch = \App\Models\LiveMatch::find($activeMatchId);
        //     if ($activeMatch && ($activeMatch->team1_id == $this->teamId || $activeMatch->team2_id == $this->teamId)) {
        //         $this->foundMatch = $activeMatch;
        //         $this->searchStatus = $activeMatch->status;
        //     } else {
        //         // Если матч не найден или не принадлежит этой команде, удаляем из сессии
        //         session()->forget('active_match_id');
        //     }
        // }

        // Если матч не найден в сессии, проверяем в базе данных
        // if (!$this->foundMatch) {
        //     $this->checkForActiveMatch();
        // }

        // Проверяем, есть ли статус поиска в сессии
        // $searchStatus = session('search_status');
        // if ($searchStatus) {
        //     $this->searchStatus = $searchStatus;
        // }
    }

    // protected function checkForActiveMatch()
    // {
    //     // Проверяем, есть ли активный матч для этой команды
    //     $activeMatch = \App\Models\LiveMatch::where(function($query) {
    //         $query->where('team1_id', $this->teamId)
    //               ->orWhere('team2_id', $this->teamId);
    //     })->whereIn('status', [
    //         'ready_check',
    //         'map_voting',
    //         'live'
    //     ])->first();

    //     if ($activeMatch) {
    //         $this->foundMatch = $activeMatch;
    //         $this->searchStatus = $activeMatch->status;
    //     }
    // }

    public function cancelSearch()
    {
        $this->searchStatus = 'waiting';
        $this->foundMatch = null;

        // Очищаем сессию
        session()->forget('active_match_id');
        session()->forget('search_status');
    }

    public function backToTeam()
    {
        return $this->redirect(route('create.team'), navigate: true);
    }

    public function render()
    {
        return view('livewire.find-match', [
            'teamId' => $this->teamId,
            'team' => $this->team,
            'searchStatus' => $this->searchStatus,
            'foundMatch' => $this->foundMatch,
        ]);
    }
}
