<?php

namespace App\Livewire;

use App\Models\LiveMatch;
use App\Notifications\MatchAcceptedNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class FindMatchConfirm extends Component
{
    public $matchId;
    public $teamId;
    public $foundMatch;

    public $team1;
    public $team2;

    public function mount($matchId, $teamId)
    {
        $this->matchId = $matchId;
        $this->teamId = $teamId;
        $this->foundMatch = LiveMatch::find($matchId);

        // Load team information
        if ($this->foundMatch) {
            $this->team1 = Team::find($this->foundMatch->team1_id);
            $this->team2 = Team::find($this->foundMatch->team2_id);
        }
    }

    /**
     * Get the captain of a team for the current match.
     *
     * @param int $teamId
     * @return \App\Models\TeamMember|null
     */
    private function getTeamCaptain(int $teamId): ?\App\Models\TeamMember
    {
        return \App\Models\TeamMember::where('team_id', $teamId)
            ->where('role', 'captain')
            ->first();
    }

    /**
     * Accepts the found match.
     */
    public function acceptMatch()
    {
        if (!$this->foundMatch) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        try {
            DB::beginTransaction();

            // Get the captain of the current team accepting the match
            $currentTeamCaptain = $this->getTeamCaptain($this->teamId);

            if (!$currentTeamCaptain) {
                throw new \Exception('Не найден капитан текущей команды');
            }

            // Mark the current team as ready using Eloquent
            $liveMatchReady = \App\Models\LiveMatchReady::where('match_id', $this->matchId)
                ->where('player_id', $currentTeamCaptain->player_id)
                ->first();

            if ($liveMatchReady) {
                $liveMatchReady->is_ready = true;
                $liveMatchReady->save();
            } else {
                throw new \Exception('Запись о готовности не найдена');
            }

            // Check if both teams are ready
            $team1Ready = \App\Models\LiveMatchReady::where('match_id', $this->matchId)
                ->where('player_id', $this->foundMatch->getTeam1Captain()->player_id)
                ->value('is_ready');

            $team2Ready = \App\Models\LiveMatchReady::where('match_id', $this->matchId)
                ->where('player_id', $this->foundMatch->getTeam2Captain()->player_id)
                ->value('is_ready');

            if ($team1Ready && $team2Ready) {
                // Both teams are ready, move to map voting stage
                $this->foundMatch->status = 'map_voting';
                $this->foundMatch->save();

                // *** NOTIFICATION: BOTH TEAMS ARE READY - NOTIFY BOTH CAPTAINS TO START MAP BANNING ***
                // Get User objects for both captains
                $captain1 = \App\Models\User::find($this->foundMatch->getTeam1Captain()->player_id); // Get User object here
                $captain2 = \App\Models\User::find($this->foundMatch->getTeam2Captain()->player_id); // Get User object here

                if ($captain1) {
                    $captain1->notify(new MatchAcceptedNotification($this->matchId, 'Обе команды приняли матч! Приступайте к бану карт.'));
                }
                if ($captain2) {
                    $captain2->notify(new MatchAcceptedNotification($this->matchId, 'Обе команды приняли матч! Приступайте к бану карт.'));
                }
                return redirect()->route('map.ban', ['matchId' => $this->matchId]);
            } else {
                // Only one team has accepted, notify the captain of the other team

                // Determine the ID of the opposing team's captain
                $opponentTeamId = ($this->foundMatch->team1_id == $this->teamId) ? $this->foundMatch->team2_id : $this->foundMatch->team1_id;

                $opponentCaptain = $this->getTeamCaptain($opponentTeamId);

                if ($opponentCaptain) {
                    // Get User object for the opposing captain
                    $opponentUser = \App\Models\User::find($opponentCaptain->player_id);

                    if ($opponentUser) {
                        // Send a notification to the opposing captain that their partner has accepted the match
                        $opponentUser->notify(new MatchAcceptedNotification($this->matchId, 'Ваш соперник принял матч! Ожидание вашего подтверждения.'));
                    }
                }

                // Dispatch the TeamAcceptedMatch event (this event may be for updating the UI if you are listening to it)
                event(new \App\Events\TeamAcceptedMatch($this->matchId, $this->teamId));
                session()->flash('success', 'Вы приняли матч. Ожидание соперника...');

                // Send a notification to the captain of your team about accepting the match
                $currentTeamCaptain = $this->getTeamCaptain($this->teamId);

                if ($currentTeamCaptain) {
                    $currentUser = \App\Models\User::find($currentTeamCaptain->player_id);
                    if ($currentUser) {
                        $currentUser->notify(new MatchAcceptedNotification($this->matchId, 'Вы приняли матч! Ожидание подтверждения от соперника.'));
                    }
                }
            }

            $this->dispatch('$refresh');
            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Ошибка при принятии матча: ' . $e->getMessage());
        }
    }

    /**
     * Declines the current match.
     */
    public function declineMatch()
    {
        if (!$this->foundMatch) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        try {
            DB::beginTransaction();

            // Get the match ID
            $matchId = $this->foundMatch->id;

            // Delete the match
            $this->foundMatch->delete();

            // Delete the readiness records
            \App\Models\LiveMatchReady::where('match_id', $matchId)->delete();

            DB::commit();

            session()->flash('info', 'Вы отклонили матч');

        } catch (\Exception $e) {
            DB::rollBack();
            session()->flash('error', 'Ошибка при отклонении матча: ' . $e->getMessage());
        }

        // Send a notification to the team captains about the match being declined
        $team1Captain = $this->foundMatch->getTeam1Captain();
        $team2Captain = $this->foundMatch->getTeam2Captain();

        if ($team1Captain) {
            $captain1User = \App\Models\User::find($team1Captain->player_id);
            if ($captain1User) {
                $captain1User->notify(new MatchAcceptedNotification($matchId, 'Соперник отклонил матч.'));
            }
        }

        if ($team2Captain) {
            $captain2User = \App\Models\User::find($team2Captain->player_id);
            if ($captain2User) {
                $captain2User->notify(new MatchAcceptedNotification($matchId, 'Соперник отклонил матч.'));
            }
        }
    }

    public function render()
    {
        return view('livewire.find-match-confirm');
    }
}