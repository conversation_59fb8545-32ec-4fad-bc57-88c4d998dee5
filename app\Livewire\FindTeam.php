<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Team;
use App\Models\TeamInvitation;
use App\Events\TeamMemberJoined;
use App\Models\TeamMember;
use App\Models\JoinRequest;
use Livewire\Attributes\On;
use Livewire\Attributes\Title;
use Livewire\WithPagination;

#[Title('Поиск команды')]
class FindTeam extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';
    
    public $selectedGameId = 1;
    public $searchTerm = '';
    public $sentRequests = [];
    public $receivedInvitations = [];
    
    #[On('gameChanged')]
    public function updateSelectedGame($gameId)
    {
        $this->selectedGameId = $gameId;
        
        // Проверяем, является ли пользователь членом команды для выбранной игры
        if (auth()->check() && $this->selectedGameId && auth()->user()->hasTeamForGame($this->selectedGameId)) {
            // Перенаправляем на страницу create.team
            return $this->redirect(route('create.team'), navigate: true);
        }
        
        $this->resetPage();
        $this->loadSentRequests();
        $this->loadReceivedInvitations();
    }
    
    public function mount($gameId = null)
    {
        // Если gameId передан через URL, используем его
        if ($gameId) {
            $this->selectedGameId = $gameId;
            // Сохраняем в сессии
            session(['selectedGameId' => $gameId]);
        } 
        // Иначе пробуем получить из сессии
        else if (session()->has('selectedGameId')) {
            $this->selectedGameId = session('selectedGameId');
        }
        // Иначе используем значение по умолчанию
        else {
            $this->selectedGameId = 1;
        }
        
        try {
            // Проверяем, является ли пользователь членом команды для выбранной игры
            if (auth()->check() && $this->selectedGameId && auth()->user()->hasTeamForGame($this->selectedGameId)) {
                // Перенаправляем на страницу create.team
                return $this->redirect(route('create.team'), navigate: true);
            }
        } catch (\Exception $e) {
            
            // Не перенаправляем в случае ошибки
        }
        
        if ($this->selectedGameId) {
            $this->loadSentRequests();
            $this->loadReceivedInvitations();
        }
        
        // Отправляем событие для обновления карусели
        $this->dispatch('gameSelected', gameId: $this->selectedGameId);
    }
    
    public function loadSentRequests()
    {
        if (auth()->check()) {
            $this->sentRequests = JoinRequest::where('player_id', auth()->id())
                ->where('status', 'pending')
                ->pluck('team_id')
                ->toArray();
        } else {
            $this->sentRequests = [];
        }
    }
    
    /**
     * Загрузить список приглашений, полученных пользователем
     */
    public function loadReceivedInvitations()
    {
        if (auth()->check()) {
            $this->receivedInvitations = TeamInvitation::where('player_id', auth()->id())
                ->where('status', 'pending')
                ->with(['team', 'team.captain'])
                ->get();
        }
    }
    
    /**
     * Принять приглашение в команду
     */
    public function acceptInvitation($invitationId)
    {
        $invitation = TeamInvitation::find($invitationId);
        
        if (!$invitation || $invitation->player_id !== auth()->id()) {
            session()->flash('error', 'Приглашение не найдено');
            return;
        }
        
        // Проверяем, есть ли уже команда у пользователя для этой игры
        $team = Team::find($invitation->team_id);
        if (auth()->user()->hasTeamForGame($team->game_id)) {
            session()->flash('error', 'Вы уже состоите в команде для этой игры');
            return;
        }
        
        // Удаляем все существующие приглашения для этой команды и пользователя
        TeamInvitation::where('team_id', $invitation->team_id)
            ->where('player_id', auth()->id())
            ->where('id', '!=', $invitationId) // Не удаляем текущее приглашение
            ->delete();
        
        // Добавляем пользователя в команду через модель TeamMember
        TeamMember::create([
            'team_id' => $invitation->team_id,
            'player_id' => auth()->id(), // Используем player_id вместо user_id
            'role' => 'member'
        ]);
        
        // Обновляем статус приглашения
        $invitation->update(['status' => 'accepted']);
        
        // Обновляем список приглашений
        $this->loadReceivedInvitations();
        
        // Отправляем событие через broadcasting
        event(new \App\Events\TeamMemberJoined($invitation->team_id));
        
        session()->flash('success', 'Вы успешно присоединились к команде');
    }
    
    /**
     * Отклонить приглашение в команду
     */
    public function declineInvitation($invitationId)
    {
        $invitation = TeamInvitation::find($invitationId);
        
        if (!$invitation || $invitation->player_id !== auth()->id()) {
            session()->flash('error', 'Приглашение не найдено');
            return;
        }
        
        // Обновляем статус приглашения
        $invitation->update(['status' => 'declined']);
        
        // Обновляем список приглашений
        $this->loadReceivedInvitations();
        
        session()->flash('success', 'Приглашение отклонено');
    }
    
    public function sendRequest($teamId)
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }
        
        // Проверяем, есть ли уже команда у пользователя для этой игры
        if (auth()->user()->hasTeamForGame($this->selectedGameId)) {
            session()->flash('error', 'Вы уже состоите в команде для этой игры');
            return;
        }
        
        // Проверяем, не отправлял ли пользователь уже заявку в эту команду
        $existingRequest = JoinRequest::where('player_id', auth()->id())
            ->where('team_id', $teamId)
            ->where('status', 'pending')
            ->first();
            
        if ($existingRequest) {
            session()->flash('error', 'Вы уже отправили заявку в эту команду');
            return;
        }
        
        // Создаем новую заявку
        JoinRequest::create([
            'player_id' => auth()->id(),
            'team_id' => $teamId,
            'status' => 'pending'
        ]);
        
        // Обновляем список отправленных заявок
        $this->loadSentRequests();
        
        session()->flash('success', 'Заявка на вступление в команду отправлена');
    }
    
    public function cancelRequest($teamId)
    {
        // Удаляем заявку
        JoinRequest::where('player_id', auth()->id())
            ->where('team_id', $teamId)
            ->where('status', 'pending')
            ->delete();
            
        // Обновляем список отправленных заявок
        $this->loadSentRequests();
        
        session()->flash('success', 'Заявка отменена');
    }
    
    /**
     * Выйти из команды
     */
    public function leaveTeam($teamId)
    {
        // Проверяем, состоит ли пользователь в этой команде
        $teamMember = \App\Models\TeamMember::where('team_id', $teamId)
            ->where('player_id', auth()->id())
            ->first();
        
        if (!$teamMember) {
            session()->flash('error', 'Вы не состоите в этой команде');
            return;
        }
        
        // Проверяем, не является ли пользователь капитаном
        if ($teamMember->role === 'captain') {
            session()->flash('error', 'Капитан не может покинуть команду. Сначала передайте права капитана другому участнику или расформируйте команду.');
            return;
        }
        
        // Удаляем пользователя из команды
        $teamMember->delete();
        
        // Удаляем или обновляем все принятые приглашения для этой команды
        TeamInvitation::where('team_id', $teamId)
            ->where('player_id', auth()->id())
            ->where('status', 'accepted')
            ->delete(); // или можно изменить статус на 'left'
        
        // Обновляем списки команд и приглашений
        $this->loadReceivedInvitations();
        
        // Отправляем событие об изменении состава команды
        event(new \App\Events\TeamMemberLeft($teamId));
        
        session()->flash('success', 'Вы успешно покинули команду');
    }

    #[On('gameChanged')]
    public function gameChange($gameId)
    {
        $this->selectedGameId = $gameId;
    }
    
    public function render()
    {
        $teamsQuery = Team::query();
        
        // Фильтруем по выбранной игре
        if ($this->selectedGameId) {
            $teamsQuery->where('game_id', $this->selectedGameId);
        }
        
        // Фильтруем по поисковому запросу
        if ($this->searchTerm) {
            $teamsQuery->where('name', 'like', '%' . $this->searchTerm . '%');
        }
        
        // Исключаем команды, в которых пользователь уже состоит
        if (auth()->check()) {
            $userTeamIds = auth()->user()->teams()->pluck('team_id')->toArray();
            if (!empty($userTeamIds)) {
                $teamsQuery->whereNotIn('id', $userTeamIds);
            }
        }
        
        $teams = $teamsQuery->with(['captain', 'members'])
            ->withCount('members')
            ->orderBy('rating', 'desc')
            ->paginate(10);
            
        return view('livewire.find-team', [
            'teams' => $teams
        ]);
    }
}













