<?php

namespace App\Livewire;

use App\Models\LiveMatch;
use App\Models\Team;
use App\Models\TeamMember;
use App\Models\Cs2Map;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class LiveMatchManager extends Component
{
    public $matchId;
    public $match;
    public $currentUser;
    public $userTeam;
    public $opponentTeam;
    public $availableMaps = [];
    public $bannedMaps = [];
    public $currentVotingTeam;
    public $isUserTurn = false;
    public $matchStatus;
    public $readyStatus = [];
    
    protected $listeners = [
        'refreshMatch' => 'refreshMatchData',
        'matchUpdated' => 'handleMatchUpdate'
    ];

    public function mount($matchId = null)
    {
        $this->matchId = $matchId;
        $this->currentUser = Auth::user();
        
        if ($this->matchId) {
            $this->loadMatchData();
        }
    }

    public function loadMatchData()
    {
        $this->match = LiveMatch::with(['team1', 'team2'])->find($this->matchId);
        
        if (!$this->match) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        $this->matchStatus = $this->match->status;
        
        // Определяем команды пользователя и соперника
        $this->determineTeams();
        
        // Загружаем данные в зависимости от статуса матча
        if ($this->matchStatus === MatchStatus::READY_CHECK->value) {
            $this->loadReadyCheckData();
        } elseif ($this->matchStatus === MatchStatus::MAP_VOTING->value) {
            $this->loadMapVotingData();
        }
    }

    private function determineTeams()
    {
        if ($this->match->team1_id == $this->userTeam?->id) {
            $this->userTeam = $this->match->team1;
            $this->opponentTeam = $this->match->team2;
        } else {
            $this->userTeam = $this->match->team2;
            $this->opponentTeam = $this->match->team1;
        }
    }

    private function loadReadyCheckData()
    {
        // Проверяем готовность команд
        $this->readyStatus = [
            'user_team' => $this->match->isTeamReady($this->userTeam->id),
            'opponent_team' => $this->match->isTeamReady($this->opponentTeam->id)
        ];
    }

    private function loadMapVotingData()
    {
        // Загружаем доступные карты
        $this->availableMaps = Cs2Map::where('is_active', true)->get();
        
        // Загружаем забаненные карты
        $this->bannedMaps = $this->match->getBannedMapIds();
        
        // Определяем текущую голосующую команду
        $this->currentVotingTeam = $this->match->getCurrentVotingTeam();
        
        // Проверяем, ход ли пользователя
        $this->isUserTurn = $this->currentVotingTeam && $this->currentVotingTeam->id == $this->userTeam->id;
    }

    public function confirmReady()
    {
        try {
            // Проверяем, что пользователь является капитаном
            $teamMember = TeamMember::where('player_id', $this->currentUser->id)
                ->whereIn('team_id', [$this->match->team1_id, $this->match->team2_id])
                ->where('role', 'captain')
                ->first();

            if (!$teamMember) {
                session()->flash('error', 'Только капитан команды может подтвердить готовность');
                return;
            }

            // Проверяем, что матч в статусе подтверждения готовности
            if ($this->match->status !== MatchStatus::READY_CHECK->value) {
                session()->flash('error', 'Матч не в статусе подтверждения готовности');
                return;
            }

            // Проверяем, не подтверждал ли уже капитан готовность
            $existingReady = $this->match->readyChecks()
                ->where('player_id', $this->currentUser->id)
                ->exists();

            if ($existingReady) {
                session()->flash('error', 'Готовность уже подтверждена');
                return;
            }

            // Подтверждаем готовность
            $this->match->readyChecks()->create([
                'player_id' => $this->currentUser->id,
                'is_ready' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            Log::info('Подтверждена готовность команды', [
                'match_id' => $this->match->id,
                'team_id' => $teamMember->team_id,
                'player_id' => $this->currentUser->id
            ]);

            // Проверяем, готовы ли обе команды
            if ($this->match->areBothTeamsReady()) {
                $this->match->startMapVoting();
                $this->matchStatus = $this->match->status;
                $this->loadMapVotingData();
                
                session()->flash('success', 'Обе команды готовы! Начинается голосование по картам');
            } else {
                $this->loadReadyCheckData();
                session()->flash('success', 'Готовность подтверждена. Ожидание второй команды...');
            }

            $this->dispatch('matchUpdated');

        } catch (\Exception $e) {
            Log::error('Ошибка при подтверждении готовности: ' . $e->getMessage());
            session()->flash('error', 'Ошибка при подтверждении готовности');
        }
    }

    public function banMap($mapId)
    {
        try {
            // Проверяем, что матч в статусе голосования по картам
            if ($this->match->status !== MatchStatus::MAP_VOTING->value) {
                session()->flash('error', 'Матч не в статусе голосования по картам');
                return;
            }

            // Проверяем, является ли пользователь капитаном команды, которая сейчас голосует
            if (!$this->isUserTurn) {
                session()->flash('error', 'Сейчас не ваш ход для голосования');
                return;
            }

            // Проверяем, не забанена ли уже эта карта
            if (in_array($mapId, $this->bannedMaps)) {
                session()->flash('error', 'Эта карта уже забанена');
                return;
            }

            // Проверяем, что карта активна
            $map = Cs2Map::find($mapId);
            if (!$map || !$map->is_active) {
                session()->flash('error', 'Карта не найдена или неактивна');
                return;
            }

            // Баним карту
            DB::table('live_match_banned_maps')->insert([
                'match_id' => $this->match->id,
                'map_id' => $mapId,
                'team_id' => $this->userTeam->id,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            Log::info('Забанена карта', [
                'match_id' => $this->match->id,
                'map_id' => $mapId,
                'map_name' => $map->name,
                'team_id' => $this->userTeam->id
            ]);

            // Переключаем ход
            $this->match->switchVoter();

            // Обновляем данные
            $this->loadMapVotingData();

            // Проверяем, осталась ли только одна карта
            if ($this->match->hasOnlyOneMapLeft()) {
                $this->match->selectFinalMap();
                $this->matchStatus = $this->match->status;
                
                session()->flash('success', 'Выбрана финальная карта! Игра начинается');
            } else {
                session()->flash('success', 'Карта забанена. Ход переходит к сопернику');
            }

            $this->dispatch('matchUpdated');

        } catch (\Exception $e) {
            Log::error('Ошибка при бане карты: ' . $e->getMessage());
            session()->flash('error', 'Ошибка при бане карты');
        }
    }

    public function refreshMatchData()
    {
        $this->loadMatchData();
    }

    public function handleMatchUpdate()
    {
        $this->loadMatchData();
    }

    public function getAvailableMapsForVoting()
    {
        if (!$this->availableMaps) {
            return collect();
        }

        return collect($this->availableMaps)->filter(function($map) {
            return !in_array($map->id, $this->bannedMaps);
        });
    }

    public function getBannedMapsInfo()
    {
        if (!$this->availableMaps || !$this->bannedMaps) {
            return collect();
        }

        return collect($this->availableMaps)->filter(function($map) {
            return in_array($map->id, $this->bannedMaps);
        });
    }

    public function render()
    {
        return view('livewire.live-match-manager');
    }
} 