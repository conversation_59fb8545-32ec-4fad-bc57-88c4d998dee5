<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Team;
use App\Models\LiveMatch;
use App\Events\MapBanned;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class MapBan extends Component
{
    public LiveMatch $match;
    public Team $team;
    public $teamId;
    public $opponents;
    public $bannedMaps = [];
    public $availableMaps = [];
    public $currentVotingTeam = null;
    public $maxBans = 6;

    public function mount(LiveMatch $match)
    {
        $this->match = $match;
        
        // Получаем команду текущего пользователя
        $this->team = Auth::user()->teams()->first();
        $this->teamId = $this->team->id;
        
        // Определяем команду соперника
        $opponentTeamId = ($this->match->team1_id == $this->teamId) 
            ? $this->match->team2_id 
            : $this->match->team1_id;
            
        $this->opponents = Team::find($opponentTeamId);
        
        $this->loadAvailableMaps();
        $this->syncBannedMaps();
        $this->refreshVotingTeam();
    }    

    public function banMap($mapId)
    {
        if (!$this->match) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        if ($this->match->current_voting_team != $this->teamId) {
            session()->flash('error', 'Сейчас не ваша очередь банить карту');
            return;
        }

        // Проверяем, что карта еще не забанена
        if (in_array($mapId, $this->bannedMaps)) {
            session()->flash('error', 'Эта карта уже забанена');
            return;
        }

        // Используем метод модели для бана карты
        $success = $this->match->banMap($mapId, $this->teamId);

        if (!$success) {
            session()->flash('error', 'Не удалось забанить карту');
            return;
        }

        // Определяем следующую голосующую команду
        $opponentTeamId = ($this->match->team1_id == $this->teamId)
            ? $this->match->team2_id
            : $this->match->team1_id;

        // Отправляем событие
        $opponentCaptain = \App\Models\TeamMember::where('team_id', $opponentTeamId)
            ->where('role', 'captain')
            ->first();

        if ($opponentCaptain) {
            event(new MapBanned(
                matchId: $this->match->id,
                mapId: $mapId,
                fromCaptainId: Auth::id(),
                toCaptainId: $opponentCaptain->player_id,
                nextVotingTeamId: $opponentTeamId
            ));
        }

        // Обновляем локальные данные
        $this->syncBannedMaps();
        $this->refreshVotingTeam();
        $this->loadAvailableMaps();

        // Проверяем, нужно ли завершить баны
        $this->checkIfBanningComplete();
    }

    #[On('map.banned')]
    public function handleMapBanned($event)
    {
        Log::info('Получено событие о бане карты', ['event' => $event]);
        
        if ($this->match && $this->match->id === (int)$event['matchId']) {
            $this->syncBannedMaps();
            $this->refreshVotingTeam();
            $this->loadAvailableMaps();
            
            $this->dispatch('playNotificationSound');
            session()->flash('info', 'Оппонент забанил карту. Ваша очередь!');
        }
    }

    protected function refreshVotingTeam()
    {
        if ($this->match) {
            $this->match->refresh();
            $this->currentVotingTeam = Team::find($this->match->current_voting_team);
        }
    }

    protected function syncBannedMaps()
    {
        if ($this->match) {
            try {
                $this->bannedMaps = $this->match->bannedMaps()->pluck('map_id')->toArray();
            } catch (\Exception $e) {
                Log::error('Ошибка при синхронизации забаненных карт: ' . $e->getMessage());
                $this->bannedMaps = [];
            }
        }
    }

    protected function loadAvailableMaps()
    {
        $this->availableMaps = \App\Models\Cs2Map::where('is_active', true)
            ->get(); // Теперь получаем коллекцию моделей, а не массив
    }

    /**
     * Разбанить карту (для тестирования)
     */
    public function unbanMap($mapId)
    {
        if (!$this->match) {
            session()->flash('error', 'Матч не найден');
            return;
        }

        // Удаляем бан из базы данных
        $this->match->bannedMaps()->where('map_id', $mapId)->delete();

        // Обновляем локальные данные
        $this->syncBannedMaps();
        $this->loadAvailableMaps();

        session()->flash('success', 'Бан карты снят');
    }

    /**
     * Проверяет, завершены ли баны карт
     */
    protected function checkIfBanningComplete()
    {
        $totalMaps = \App\Models\Cs2Map::where('is_active', true)->count();
        $bannedCount = count($this->bannedMaps);
        $remainingMaps = $totalMaps - $bannedCount;

        Log::info('Проверка завершения банов', [
            'match_id' => $this->match->id,
            'total_maps' => $totalMaps,
            'banned_count' => $bannedCount,
            'remaining_maps' => $remainingMaps,
            'max_bans' => $this->maxBans
        ]);

        // Если осталась только одна карта или достигнут лимит банов
        if ($remainingMaps <= 1 || $bannedCount >= $this->maxBans) {
            $finalMap = $this->match->selectFinalMap();

            if ($finalMap) {
                session()->flash('success', "Баны завершены! Выбрана финальная карта: {$finalMap->name}");

                // Можно добавить событие о завершении банов
                // event(new MapVotingCompleted($this->match->id, $finalMap->id));

                // Перенаправляем на страницу матча или показываем результат
                $this->dispatch('mapVotingCompleted', [
                    'matchId' => $this->match->id,
                    'selectedMapId' => $finalMap->id,
                    'selectedMapName' => $finalMap->name
                ]);
            } else {
                session()->flash('error', 'Ошибка при выборе финальной карты');
            }
        }
    }

    public function render()
    {
        return view('livewire.map-ban');
    }
}
