<?php

namespace App\Livewire;

use App\Models\Team;
use App\Models\LiveMatch;
use App\Models\LiveMatchReady;
use App\Models\Cs2Map;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use App\Services\MatchmakingService;
use Livewire\Component;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Events\MatchAccepted;
use Livewire\Attributes\On; 
use App\Events\MapBanned;

class MatchSearch extends Component
{
    public $teamId;
    public $team;
    public $searchStatus = 'waiting'; // waiting, searching, found, ready_check, map_voting, live
    public $searchTime = 0;
    public $maxSearchTime = 60;
    public $foundMatch = null;
    public $opponents = [];
    public $bannedMaps = [];
    public $availableMaps = [];
    public $currentVotingTeam = null;
    public $maxBans = 6;
    
    public $showAcceptModal = false; 
    public $matchData = null; 

    public function mount($teamId, $searchStatus = 'waiting', $foundMatch = null)
    {
        $this->teamId = $teamId;
        $this->team = \App\Models\Team::find($teamId);
        $this->searchStatus = $searchStatus;
        $this->foundMatch = $foundMatch;
        $this->opponents = [];
        $this->bannedMaps = [];
        
        if ($this->team) {
            $this->loadAvailableMaps();
        }
    }

    // Новый метод для wire:init
    public function loadComponent()
    {
        // Здесь может быть любая логика, которая должна выполняться после полной загрузки компонента,
        // но для отображения спиннера достаточно оставить его пустым.
    }

    protected function loadAvailableMaps()
    {
        if ($this->team && $this->team->game_id) {
            switch ($this->team->game_id) {
                case 1: // CS2
                    $maps = Cs2Map::where('is_active', true)->get();
                    $this->availableMaps = $maps->map(function($map) {
                        return [
                            'id' => $map->id,
                            'name' => $map->name,
                            'image_url' => $map->image_url
                        ];
                    })->toArray();
                    break;
                case 2: // Dota 2
                    $this->availableMaps = [
                        ['id' => 1, 'name' => 'Default Map', 'image_url' => null],
                    ];
                    break;
                case 3: // PUBG
                    $this->availableMaps = [
                        ['id' => 1, 'name' => 'Erangel', 'image_url' => null],
                        ['id' => 2, 'name' => 'Miramar', 'image_url' => null],
                        ['id' => 3, 'name' => 'Sanhok', 'image_url' => null],
                        ['id' => 4, 'name' => 'Vikendi', 'image_url' => null],
                        ['id' => 5, 'name' => 'Karakin', 'image_url' => null],
                        ['id' => 6, 'name' => 'Paramo', 'image_url' => null],
                        ['id' => 7, 'name' => 'Haven', 'image_url' => null],
                    ];
                    break;
                default:
                    $this->availableMaps = [];
            }
        } else {
            $this->availableMaps = [];
        }
    }

    public function startSearch()
    {        
        $teamMember = \App\Models\TeamMember::where('team_id', $this->teamId)
            ->where('player_id', Auth::id())
            ->where('role', 'captain')
            ->first();

        if (!$teamMember) {
            session()->flash('error', 'Только капитан команды может начать поиск матча.');
            return;
        }

        $this->searchStatus = 'searching';
        $this->searchTime = 0;
        
        $this->dispatch('startSearchTimer');

        session(['search_status' => 'searching']);

        $this->findMatch(app(MatchmakingService::class));
    }

    public function updateSearchTime()
    {
        $this->searchTime++;
    }

    public function findMatch(MatchmakingService $matchmakingService)
    {
        try {
            Log::info('Начало поиска матча', ['team_id' => $this->teamId]);
    
            $match = $matchmakingService->findMatch($this->team);
    
            if ($match) {
                Log::info('Матч найден', ['match_id' => $match->id]);
                $this->foundMatch = $match;
                $this->searchStatus = 'ready_check';
    
                $opponentTeamId = ($match->team1_id == $this->teamId) ? $match->team2_id : $match->team1_id;
                $this->opponents = [
                    'team' => Team::find($opponentTeamId)
                ];
    
                $this->dispatch('stopSearchTimer');
            } else {
                Log::warning('Матч не найден');
                session()->flash('error', 'Не удалось найти матч. Попробуйте позже.');
                $this->searchStatus = 'waiting'; 
                $this->dispatch('stopSearchTimer');
            }
        } catch (\Exception $e) {
            Log::error('Ошибка при поиске матча: ' . $e->getMessage(), ['exception' => $e, 'trace' => $e->getTraceAsString()]);
            session()->flash('error', 'Произошла ошибка при поиске матча. Пожалуйста, попробуйте еще раз.');
            $this->searchStatus = 'error';
        }
    }

    public function cancelSearch()
    {
        $this->searchStatus = 'waiting';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];
        session()->forget('search_status');
        $this->dispatch('stopSearchTimer');
    }

    #[On('showMatchAcceptModal')]
    public function handleShowMatchAcceptModal($matchId)
    {
        Log::info('Received showMatchAcceptModal event in PHP', ['matchId' => $matchId]);

        if ($matchId) {
            $this->foundMatch = LiveMatch::find($matchId); 
            if ($this->foundMatch) {
                $this->searchStatus = 'ready_check';
                
                $opponentTeamId = ($this->foundMatch->team1_id == $this->teamId) 
                    ? $this->foundMatch->team2_id 
                    : $this->foundMatch->team1_id;
                $this->opponents = ['team' => Team::find($opponentTeamId)];
                
                $this->checkTeamsReadyStatus(); 
                
                if ($this->searchStatus === 'ready_check') {
                    $this->showAcceptModal = true; 
                    $this->matchData = $matchId; 
                } else {
                    $this->showAcceptModal = false;
                }

            } else {
                Log::error('Match not found after receiving showMatchAcceptModal event', ['matchId' => $matchId]);
                session()->flash('error', 'Информация о матче не найдена.');
            }
        } else {
            Log::error('matchId missing in showMatchAcceptModal event', ['matchId' => $matchId]);
        }
    }


    public function acceptMatch()
    {
        try {
            if (!$this->foundMatch) {
                Log::error('Match not found');
                session()->flash('error', 'Матч не найден');
                return;
            }
    
            $teamCaptain = \App\Models\TeamMember::where('team_id', $this->teamId)
                ->where('player_id', Auth::id())
                ->where('role', 'captain')
                ->first();
    
            $opponentTeamId = ($this->foundMatch->team1_id == $this->teamId) 
                ? $this->foundMatch->team2_id 
                : $this->foundMatch->team1_id;
    
            $opponentCaptain = \App\Models\TeamMember::where('team_id', $opponentTeamId)
                ->where('role', 'captain')
                ->first();
    
            if (!$teamCaptain || !$opponentCaptain) {
                Log::error('Captains not found', ['team_id' => $this->teamId, 'opponent_team_id' => $opponentTeamId]);
                session()->flash('error', 'Не найдены капитаны команд');
                return;
            }
    
            DB::table('live_match_ready')->updateOrInsert(
                [
                    'match_id' => $this->foundMatch->id,
                    'player_id' => $teamCaptain->player_id,
                ],
                [
                    'is_ready' => true,
                ]
            );
    
            event(new MatchAccepted(
                $this->foundMatch->id,
                $teamCaptain->player_id,
                $opponentCaptain->player_id
            ));
    
            $this->checkTeamsReadyStatus();
            
        } catch (\Exception $e) {
            Log::error('Error in acceptMatch: ' . $e->getMessage());
            session()->flash('error', 'Ошибка при принятии матча');
        }
    }
    
    protected function checkTeamsReadyStatus()
    {
        $this->foundMatch->refresh(); 
                
        if ($this->foundMatch->areBothTeamsReady()) {
            if ($this->foundMatch->status !== MatchStatus::MAP_VOTING->value) {
                $this->foundMatch->status = MatchStatus::MAP_VOTING->value;
                $this->foundMatch->save();
            }
            
            $this->searchStatus = 'map_voting';
            $this->currentVotingTeam = $this->foundMatch->getCurrentVotingTeam();
            $this->loadAvailableMaps();
            $this->showAcceptModal = false; // Закрываем модальное окно, если обе команды готовы
            session()->forget('info'); // Очищаем сообщение "Ожидание готовности второй команды"
            
        } else {
            $this->searchStatus = 'ready_check';
            session()->flash('info', 'Ожидание готовности второй команды.');
        }
    }
    
    public function declineMatch(): void
    {
        if ($this->foundMatch) {
            $this->foundMatch->delete();
        }

        $this->searchStatus = 'waiting';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];
        session()->forget('search_status');

        $this->showAcceptModal = false;
    }

    public function findNewMatch()
    {
        $this->searchStatus = 'searching';
        $this->searchTime = 0;
        $this->foundMatch = null;
        $this->opponents = [];

        $this->findMatch(app(MatchmakingService::class));
    }

    // Обработчик события map.banned удален - обработка теперь только в MapBan компоненте

    // Метод banMap удален - логика бана карт теперь только в MapBan компоненте

    // Методы syncBannedMaps и unbanMap удалены - логика работы с банами карт теперь только в MapBan компоненте

    /**
     * Обновляет информацию о текущей голосующей команде
     */
    protected function refreshVotingTeam()
    {
        if ($this->foundMatch) {
            $this->foundMatch->refresh(); // Обновляем данные из БД
            $this->currentVotingTeam = Team::find($this->foundMatch->current_voting_team);
            
            // Логируем изменение для отладки
            Log::info('Обновлена голосующая команда', [
                'match_id' => $this->foundMatch->id,
                'current_voting_team' => $this->currentVotingTeam?->id,
                'team_name' => $this->currentVotingTeam?->name
            ]);
            
            // Обновляем состояние компонента
            $this->syncBannedMaps();
        }
    }

    public function render()
    {
        if (!is_array($this->availableMaps)) {
            $this->availableMaps = [];
        }
        
        if (!is_array($this->bannedMaps)) {
            $this->bannedMaps = [];
        }
        
        if (!is_array($this->opponents)) {
            $this->opponents = [];
        }
        
        return view('livewire.match-search');
    }
}