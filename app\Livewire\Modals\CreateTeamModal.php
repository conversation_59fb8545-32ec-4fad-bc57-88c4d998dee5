<?php

namespace App\Livewire\Modals;

use Livewire\Component;
use App\Models\Team;
use App\Models\TeamMember;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;

class CreateTeamModal extends Component
{
    public $selectedGameId;
    public $teamName = '';
    public $teamDescription = '';

    // Принимаем gameId из родительского компонента
    public function mount($selectedGameId)
    {
        $this->selectedGameId = $selectedGameId;
    }

    public function saveTeam()
    {
        $this->validate([
            'teamName' => 'required|min:3|max:50',
            'teamDescription' => 'nullable|max:255',
        ]);

        if (!$this->selectedGameId) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Сначала выберите игру']);
            return;
        }

        $user = auth()->user();

        // Проверяем, есть ли уже команда у пользователя для этой игры
        if ($user->hasTeamForGame($this->selectedGameId)) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Вы уже состоите в команде для этой игры']);
            return;
        }

        try {
            // Создаем новую команду
            $team = Team::create([
                'name' => $this->teamName,
                'description' => $this->teamDescription,
                'game_id' => $this->selectedGameId,
                'captain_id' => $user->id,
                'rating' => 0 // Начальный рейтинг
            ]);

            // Создаем запись в таблице team_members
            TeamMember::create([
                'team_id' => $team->id,
                'player_id' => $user->id,
                'role' => 'captain'
            ]);

            // Очищаем кэш, чтобы основной компонент обновил список команд
            Cache::forget("user_teams_{$user->id}_{$this->selectedGameId}");

            // Очищаем форму
            $this->reset(['teamName', 'teamDescription']);

            // Отправляем событие родительскому компоненту, что команда создана
            $this->dispatch('teamCreated');
            $this->dispatch('closeCreateTeamModal'); // Закрываем модальное окно через Alpine.js
            $this->dispatch('showNotification', ['type' => 'success', 'message' => 'Команда успешно создана!']);

        } catch (\Exception $e) {
            \Log::error('Ошибка при создании команды: ' . $e->getMessage());
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Произошла ошибка при создании команды: ' . $e->getMessage()]);
        }
    }

    public function render()
    {
        return view('livewire.modals.create-team-modal');
    }
}