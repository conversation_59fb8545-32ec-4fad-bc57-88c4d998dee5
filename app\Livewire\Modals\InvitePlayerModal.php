<?php

namespace App\Livewire\Modals;

use Livewire\Component;
use App\Models\User;
use App\Models\TeamInvitation;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class InvitePlayerModal extends Component
{
    public $teamIdForInvite = null;
    public $playerName = '';
    public $playerOptions = [];

    protected function loadPlayerOptions()
    {
        $cacheKey = 'player_options_' . Auth::id();
        
        $this->playerOptions = Cache::remember($cacheKey, 300, function() {
            return User::where('id', '!=', Auth::id())
                ->select('id', 'client_nick', 'avatar')
                ->orderBy('client_nick')
                ->get()
                ->map(function($player) {
                    return [
                        'id' => $player->id,
                        'name' => $player->client_nick,
                        'avatar' => $player->avatar
                    ];
                })->toArray();
        });
    }

    public function mount($teamIdForInvite = null)
    {
        $this->teamIdForInvite = $teamIdForInvite;
        $this->loadPlayerOptions();
    }

    #[On('open-invite-player-modal')]
    public function openModal($data)
    {
        $this->teamIdForInvite = $data['teamId'];
        $this->loadPlayerOptions();
    }

    public function sendInvitation()
    {
        $this->validate([
            'playerName' => 'required',
        ], [
            'playerName.required' => 'Выберите игрока'
        ]);
        
        $user = Auth::user();
        
        // Проверяем, является ли пользователь капитаном команды
        if (!$user->isCaptainOf($this->teamIdForInvite)) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Только капитан может приглашать игроков']);
            return;
        }
        
        // Находим пользователя по ID
        $invitedUser = User::find($this->playerName);
        
        if (!$invitedUser) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Пользователь не найден']);
            return;
        }
        
        // Проверяем, не отправлено ли уже приглашение
        $existingInvitation = TeamInvitation::where('team_id', $this->teamIdForInvite)
            ->where('player_id', $invitedUser->id)
            ->where('status', 'pending')
            ->first();
        
        if ($existingInvitation) {
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Приглашение уже отправлено этому игроку']);
            return;
        }
        
        try {
            DB::beginTransaction();
            
            // Создаем приглашение
            TeamInvitation::create([
                'team_id' => $this->teamIdForInvite,
                'player_id' => $invitedUser->id,
                'invited_by' => $user->id,
                'status' => 'pending'
            ]);
            
            DB::commit();
            
            // Очищаем кэш, чтобы родительский компонент обновил данные
            Cache::forget("user_teams_{$user->id}_{$this->teamIdForInvite}");
            
            // Очищаем форму
            $this->reset(['playerName']);
            
            // Закрываем модальное окно через Alpine.js
            $this->dispatch('closeInviteModal');
            
            // Отправляем событие родительскому компоненту, что приглашение отправлено
            $this->dispatch('invitationSent', ['teamId' => $this->teamIdForInvite]);
            
            // Показываем уведомление об успехе
            $this->dispatch('showNotification', ['type' => 'success', 'message' => 'Приглашение успешно отправлено']);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при создании приглашения: ' . $e->getMessage());
            $this->dispatch('showNotification', ['type' => 'error', 'message' => 'Произошла ошибка при отправке приглашения']);
        }
    }

    /**
     * Обработчик события изменения выбранного значения в селекте
     */
    #[On('select-changed')]
    public function handleSelectChanged($data)
    {
        if ($data['name'] === 'playerName') {
            $this->playerName = $data['value'];
        }
    }

    public function render()
    {
        return view('livewire.modals.invite-player-modal');
    }
}