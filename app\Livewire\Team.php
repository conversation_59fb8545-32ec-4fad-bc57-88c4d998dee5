<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\Title;
use Livewire\Attributes\On;
use Livewire\Attributes\Lazy;
use App\Models\GameMatch;
use App\Models\Rating;
use App\Models\Club;
use App\Models\Season;
use App\Models\FullRating;
use App\Models\SeasonRating;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

#[Title('Команда игрока')]
#[Lazy] // Добавляем ленивую загрузку компонента
class Team extends Component
{
    public $client_nick;
    public $selectedGameId = 1; // CS2
    public $selectedSeasonId = 1; // Текущий сезон
    public $isLoading = false; // Индикатор загрузки
    public $selectedMap = ''; // Добавляем свойство для карты

    #[On('gameChanged')]
    public function updateSelectedGame($gameId)
    {
        $this->isLoading = true; // Устанавливаем флаг загрузки
        
        // Обновляем рейтинг команды
        $this->selectedGameId = $gameId;
        // Сохраняем выбранную игру в сессии
        session(['selectedGameId' => $gameId]);
        
        // Обновляем карту в зависимости от игры
        $this->selectedMap = match($gameId) {
            1 => 'Dust II', // CS2
            2 => 'Ancient', // Dota 2
            3 => 'Erangel', // PUBG
            default => ''
        };
        
        $this->isLoading = false; // Снимаем флаг загрузки
    }

    public function mount()
    {
        $this->client_nick = auth()->user()->client_nick;
        // Получаем ID выбранной игры из сессии или устанавливаем значение по умолчанию
        $this->selectedGameId = session('selectedGameId', 1);
        
        // Устанавливаем начальную карту
        $this->selectedMap = match($this->selectedGameId) {
            1 => 'Dust II', // CS2
            2 => 'Ancient', // Dota 2
            3 => 'Erangel', // PUBG
            default => ''
        };
    }

    public function saveName()
    {
        $this->validate([
            'client_nick' => 'required|string|min:3|max:255',
        ], [
            'client_nick.required' => 'Пожалуйста, введите имя игрока',
            'client_nick.string' => 'Имя должно быть текстом',
            'client_nick.min' => 'Имя должно содержать минимум 3 символа',
            'client_nick.max' => 'Имя не должно превышать 255 символов',
        ]);
        
        $user = Auth::user();
        
        // Проверяем, изменилось ли имя
        if ($user->client_nick !== $this->client_nick) {
            $user->client_nick = $this->client_nick;
            $user->save();
            
            session()->flash('success', 'Имя успешно обновлено');
            
            // Сбрасываем кэш для данного пользователя
            $this->clearUserCache();
        }
        
        // Вызываем событие для переинициализации tooltips
        $this->dispatch('nameSaved');
    }

    public function changeUser()
    {
        return $this->redirect('/login', navigate: true);
    }

    public function createTam()
    {
        return $this->redirect('/create/team', navigate:true);
    }

    public function gotoRating($type)
    {
        // Получаем позицию игрока в рейтинге
        $position = $this->getPlayerRank();
        
        // Формируем URL с параметрами
        $url = route('rating', [
            'type' => $type,
            'highlight' => auth()->id(),
            'scroll' => $position
        ]);

        return $this->redirect($url, navigate: true);
    }

    // Кэшируем результат на 5 минут
    public function getPlayerRank()
    {
        $userId = auth()->id();
        $cacheKey = "player_rank_{$userId}_{$this->selectedGameId}";
        
        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($userId) {
            // Получаем рейтинг текущего игрока
            $currentPlayerRating = $this->getPlayerRating();

            if (!$currentPlayerRating) {
                return null;
            }

            // Оптимизированный запрос с использованием индексов
            $rank = FullRating::where('game_id', $this->selectedGameId)
                ->select('player_id', DB::raw('SUM(game_rating) as total_rating'))
                ->groupBy('player_id')
                ->having('total_rating', '>', $currentPlayerRating)
                ->count();

            // Добавляем 1, так как позиция начинается с 1
            return $rank + 1;
        });
    }

    // Кэшируем результат на 5 минут
    public function getClubRating()
    {
        $userId = auth()->id();
        $clubId = auth()->user()->club_id ?? 0;
        $cacheKey = "club_rank_{$userId}_{$this->selectedGameId}_{$clubId}";
        
        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($userId, $clubId) {
            // Получаем рейтинг текущего игрока по клубу
            $currentPlayerRating = $this->getPlayerClubRating();

            if (!$currentPlayerRating) {
                return null;
            }

            // Оптимизированный запрос с использованием индексов
            $rank = FullRating::where('game_id', $this->selectedGameId)
                ->where('club_id', $clubId)
                ->select('player_id', DB::raw('SUM(game_rating) as total_rating'))
                ->groupBy('player_id')
                ->having('total_rating', '>', $currentPlayerRating)
                ->count();

            // Добавляем 1, так как позиция начинается с 1
            return $rank + 1;
        });
    }
    
    // Кэшируем рейтинг игрока
    protected function getPlayerRating()
    {
        $userId = auth()->id();
        $cacheKey = "player_rating_{$userId}_{$this->selectedGameId}";
        
        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($userId) {
            return FullRating::where('player_id', $userId)
                ->where('game_id', $this->selectedGameId)
                ->sum('game_rating');
        });
    }
    
    // Кэшируем рейтинг игрока в клубе
    protected function getPlayerClubRating()
    {
        $userId = auth()->id();
        $clubId = auth()->user()->club_id ?? 0;
        $cacheKey = "player_club_rating_{$userId}_{$this->selectedGameId}_{$clubId}";
        
        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($userId, $clubId) {
            return FullRating::where('player_id', $userId)
                ->where('game_id', $this->selectedGameId)
                ->where('club_id', $clubId)
                ->sum('game_rating');
        });
    }
    
    // Метод для очистки кэша пользователя
    protected function clearUserCache()
    {
        $userId = auth()->id();
        $gameIds = [1, 2, 3]; // Предполагаемые ID игр
        $clubId = auth()->user()->club_id ?? 0;
        
        foreach ($gameIds as $gameId) {
            Cache::forget("player_rating_{$userId}_{$gameId}");
            Cache::forget("player_rank_{$userId}_{$gameId}");
            Cache::forget("player_club_rating_{$userId}_{$gameId}_{$clubId}");
            Cache::forget("club_rank_{$userId}_{$gameId}_{$clubId}");
        }
    }

    public function render()
    {
        // Кэшируем все данные на 5 минут
        $cacheKey = "team_data_" . auth()->id() . "_" . $this->selectedGameId;
        
        $data = Cache::remember($cacheKey, now()->addMinutes(5), function () {
            $userId = auth()->id();
            
            // Получаем матчи в зависимости от выбранной игры
            $recentMatches = match($this->selectedGameId) {
                1 => \App\Models\GameMatch::where('player_id', $userId)
                    ->select('id', 'player_id', 'date', 'victory', 'score', 'math_score')
                    ->orderBy('date', 'desc')
                    ->take(3)
                    ->get(),
                2 => \App\Models\Dota2::where('player_id', $userId)
                    ->select('id', 'player_id', 'date', 'victory', 'score', 'math_score', 'hero_id')
                    ->orderBy('date', 'desc')
                    ->take(3)
                    ->get(),
                3 => \App\Models\Pubg::where('player_id', $userId)
                    ->select('id', 'player_id', 'date', 'victory', 'score', 'math_score', 'gamemode', 'mapname')
                    ->orderBy('date', 'desc')
                    ->take(3)
                    ->get(),
                default => collect([])
            };
            
            // Получаем рейтинги одним запросом
            $ratings = FullRating::where('player_id', $userId)
                ->where('game_id', $this->selectedGameId)
                ->select(DB::raw('SUM(game_rating) as total_rating'))
                ->first();
            
            $teamRating = $ratings->total_rating ?? 0;
            
            // Получаем рейтинг сезона
            $seasonRating = SeasonRating::where('player_id', $userId)
                ->where('event_id', $this->selectedSeasonId)
                ->value('rating') ?? 0;
            
            // Получаем позицию в рейтинге
            $playerRank = FullRating::where('game_id', $this->selectedGameId)
                ->select('player_id', DB::raw('SUM(game_rating) as total_rating'))
                ->groupBy('player_id')
                ->having('total_rating', '>', $teamRating)
                ->count() + 1;
            
            return [
                'recentMatches' => $recentMatches,
                'teamRating' => $teamRating,
                'seasonRating' => $seasonRating,
                'playerRank' => $playerRank
            ];
        });
        
        return view('livewire.team', array_merge($data, [
            'isLoading' => $this->isLoading,
        ]));
    }
}










