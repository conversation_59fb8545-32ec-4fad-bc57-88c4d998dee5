<?php

namespace App\Livewire\Team;

use App\Models\Team;
use Livewire\Component;
use Livewire\WithPagination;

class Matches extends Component
{
    use WithPagination;

    public $team;
    public $resultFilter = '';

    public function mount(Team $team)
    {
        $this->team = $team;
    }

    public function render()
    {
        $query = $this->team->matches()
            ->select([
                'matches.*',
                'tournaments.name as tournament_name',
                'opponent_teams.name as opponent_team_name',
                'opponent_teams.id as opponent_team_id'
            ])
            ->leftJoin('tournaments', 'matches.tournament_id', '=', 'tournaments.id')
            ->leftJoin('teams as opponent_teams', function($join) {
                $join->on('matches.team1_id', '=', 'opponent_teams.id')
                    ->where('matches.team2_id', '=', $this->team->id)
                    ->orWhere(function($query) {
                        $query->on('matches.team2_id', '=', 'opponent_teams.id')
                            ->where('matches.team1_id', '=', $this->team->id);
                    });
            })
            ->orderBy('matches.date', 'desc');

        // Применяем фильтр по результатам
        if ($this->resultFilter !== '') {
            $query->where('matches.victory', $this->resultFilter);
        }

        $matches = $query->paginate(10);

        return view('livewire.team.matches', [
            'matches' => $matches
        ]);
    }
} 