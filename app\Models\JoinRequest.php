<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JoinRequest extends Model
{
    protected $table = 'join_requests';

    protected $fillable = [
        'player_id',
        'team_id',
        'status',
    ];

     /**
     * Get the team that owns the invitation.
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }
    
    /**
     * Get the user that received the invitation.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'player_id');
    }
}
