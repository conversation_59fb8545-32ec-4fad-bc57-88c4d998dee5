<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LiveMatch extends Model
{
    use HasFactory;
    
    protected $table = 'live_match';
    public $timestamps = false;
    
    protected $fillable = [
        'team1_id',
        'team2_id',
        'status',
        'current_voter',
        'current_voting_team',
        'selected_map_id'
    ];
    
    protected $casts = [
        'status' => MatchStatus::class,
        'current_voter' => 'string' // Важно: не кастуем в enum, чтобы избежать проблем с сериализацией
    ];
    
    // Таймауты согласно алгоритму
    public $ready_check_timeout = 30; // секунды для подтверждения
    public $ban_timeout = 15; // секунды для бана карты
    
    public function team1()
    {
        return $this->belongsTo(Team::class, 'team1_id');
    }
    
    public function team2()
    {
        return $this->belongsTo(Team::class, 'team2_id');
    }

    public function getTeam1Captain()
    {
        return TeamMember::where('team_id', $this->team1_id)
                         ->where('role', 'captain')
                         ->first();
    }

    public function getTeam2Captain()
    {
        return TeamMember::where('team_id', $this->team2_id)
                         ->where('role', 'captain')
                         ->first();
    }
    
    public function readyChecks()
    {
        return $this->hasMany(LiveMatchReady::class, 'match_id');
    }

    /**
     * Связь с забаненными картами
     */
    public function bannedMaps()
    {
        return $this->hasMany(LiveMatchBannedMap::class, 'match_id');
    }

    /**
     * Получает забаненные карты через Query Builder (для обратной совместимости)
     */
    public function getBannedMaps()
    {
        return DB::table('live_match_banned_maps')
            ->where('match_id', $this->id)
            ->get();
    }

    /**
     * Получает ID забаненных карт
     */
    public function getBannedMapIds()
    {
        return DB::table('live_match_banned_maps')
            ->where('match_id', $this->id)
            ->pluck('map_id')
            ->toArray();
    }
    
    /**
     * Проверяет, готова ли команда
     */
    public function isTeamReady($teamId)
    {
        // Получаем ID капитана команды
        $captain = \App\Models\TeamMember::where('team_id', $teamId)
            ->where('role', 'captain')
            ->first();
        
        if (!$captain) {
            Log::warning('Капитан команды не найден', ['team_id' => $teamId]);
            return false;
        }
        
        // Проверяем готовность по ID игрока (капитана)
        $isReady = $this->readyChecks()
            ->where('player_id', $captain->player_id)
            ->where('is_ready', true)
            ->exists();
            
        Log::info('Проверка готовности команды', [
            'team_id' => $teamId,
            'captain_player_id' => $captain->player_id,
            'is_ready' => $isReady
        ]);
        
        return $isReady;
    }
    
    /**
     * Проверяет, готовы ли обе команды
     */
    public function areBothTeamsReady(): bool
    {
        return $this->isTeamReady($this->team1_id) &&
               $this->isTeamReady($this->team2_id);
    }
    
    /**
     * Определяет первую команду для голосования на основе рейтинга
     */
    public function determineFirstVoter()
    {
        $team1Rating = $this->team1->rating ?? 0;
        $team2Rating = $this->team2->rating ?? 0;
        
        if ($team1Rating > $team2Rating) {
            return CurrentVoter::TEAM1->value;
        } elseif ($team2Rating > $team1Rating) {
            return CurrentVoter::TEAM2->value;
        } else {
            // Случайный выбор при равных рейтингах
            return rand(0, 1) ? CurrentVoter::TEAM1->value : CurrentVoter::TEAM2->value;
        }
    }
    
    /**
     * Получает команду, которая сейчас должна голосовать
     */
    public function getCurrentVotingTeam()
    {
        // Приоритет отдаем current_voting_team (ID команды)
        if ($this->current_voting_team) {
            return \App\Models\Team::find($this->current_voting_team);
        }

        // Если current_voting_team не установлен, используем current_voter
        if (!isset($this->current_voter) || !in_array($this->current_voter, [CurrentVoter::TEAM1->value, CurrentVoter::TEAM2->value])) {
            // Если значение некорректно, устанавливаем первую команду на основе рейтинга
            $this->current_voter = $this->determineFirstVoter();
            $this->current_voting_team = $this->current_voter == CurrentVoter::TEAM1->value ? $this->team1_id : $this->team2_id;
            $this->save();
        }

        // Возвращаем соответствующую команду
        if ($this->current_voter == CurrentVoter::TEAM1->value) {
            return \App\Models\Team::find($this->team1_id);
        } else {
            return \App\Models\Team::find($this->team2_id);
        }
    }
    
    /**
     * Переключает ход на другую команду
     */
    public function switchVoter()
    {
        // Проверяем, что current_voter имеет правильное значение
        if (!isset($this->current_voter) || !in_array($this->current_voter, [CurrentVoter::TEAM1->value, CurrentVoter::TEAM2->value])) {
            // Если значение некорректно, устанавливаем первую команду на основе рейтинга
            $this->current_voter = $this->determineFirstVoter();
        }

        if ($this->current_voter == CurrentVoter::TEAM1->value) {
            $this->current_voter = CurrentVoter::TEAM2->value;
            $this->current_voting_team = $this->team2_id;
        } else {
            $this->current_voter = CurrentVoter::TEAM1->value;
            $this->current_voting_team = $this->team1_id;
        }

        $this->save();

        Log::info('Ход переключен', [
            'match_id' => $this->id,
            'current_voter' => $this->current_voter,
            'current_voting_team' => $this->current_voting_team
        ]);

        return $this;
    }
    
    /**
     * Начинает процесс подтверждения готовности
     */
    public function startReadyCheck()
    {
        $this->status = MatchStatus::READY_CHECK->value;
        $this->current_voter = $this->determineFirstVoter();
        $this->current_voting_team = $this->current_voter == CurrentVoter::TEAM1->value ? $this->team1_id : $this->team2_id;
        $this->save();

        Log::info('Начат процесс подтверждения готовности', [
            'match_id' => $this->id,
            'first_voter' => $this->current_voter,
            'current_voting_team' => $this->current_voting_team
        ]);
    }
    
    /**
     * Обрабатывает таймаут подтверждения готовности
     */
    public function handleReadyCheckTimeout()
    {
        Log::info('Таймаут подтверждения готовности', ['match_id' => $this->id]);
        
        // Удаляем матч и начинаем поиск заново
        $this->delete();
        
        // Здесь можно добавить событие для уведомления команд
        // event(new ReadyCheckTimeout($this->team1_id, $this->team2_id));
    }
    
    /**
     * Начинает процесс голосования по картам
     */
    public function startMapVoting()
    {
        $this->status = MatchStatus::MAP_VOTING->value;
        $this->current_voter = $this->determineFirstVoter();
        $this->current_voting_team = $this->current_voter == CurrentVoter::TEAM1->value ? $this->team1_id : $this->team2_id;
        $this->save();

        Log::info('Начат процесс голосования по картам', [
            'match_id' => $this->id,
            'first_voter' => $this->current_voter,
            'current_voting_team' => $this->current_voting_team
        ]);
    }
    
    /**
     * Банит карту для текущей голосующей команды
     */
    public function banMap($mapId, $teamId = null)
    {
        // Если команда не указана, используем текущую голосующую команду
        if (!$teamId) {
            $teamId = $this->current_voting_team;
        }

        // Проверяем, что карта еще не забанена
        $existingBan = $this->bannedMaps()->where('map_id', $mapId)->first();
        if ($existingBan) {
            return false; // Карта уже забанена
        }

        // Создаем запись о бане
        $this->bannedMaps()->create([
            'map_id' => $mapId,
            'team_id' => $teamId
        ]);

        Log::info('Карта забанена', [
            'match_id' => $this->id,
            'map_id' => $mapId,
            'team_id' => $teamId
        ]);

        // Переключаем ход на другую команду
        $this->switchVoter();

        return true;
    }

    /**
     * Автоматически банит случайную карту при таймауте
     */
    public function autoBanMap()
    {
        $availableMaps = \App\Models\Cs2Map::where('is_active', true)->get();
        $bannedMapIds = $this->getBannedMapIds();

        // Фильтруем доступные карты
        $availableMaps = $availableMaps->filter(function($map) use ($bannedMapIds) {
            return !in_array($map->id, $bannedMapIds);
        });

        if ($availableMaps->count() > 1) {
            // Выбираем случайную карту для бана
            $randomMap = $availableMaps->random();

            $this->banMap($randomMap->id);

            Log::info('Автоматически забанена карта', [
                'match_id' => $this->id,
                'map_id' => $randomMap->id,
                'map_name' => $randomMap->name
            ]);
        }
    }
    
    /**
     * Проверяет, осталась ли только одна карта
     */
    public function hasOnlyOneMapLeft()
    {
        $availableMaps = \App\Models\Cs2Map::where('is_active', true)->get();
        $bannedMapIds = $this->getBannedMapIds();
        
        $remainingMaps = $availableMaps->filter(function($map) use ($bannedMapIds) {
            return !in_array($map->id, $bannedMapIds);
        });
        
        return $remainingMaps->count() === 1;
    }
    
    /**
     * Выбирает финальную карту и начинает игру
     */
    public function selectFinalMap()
    {
        $availableMaps = \App\Models\Cs2Map::where('is_active', true)->get();
        $bannedMapIds = $this->getBannedMapIds();

        $remainingMaps = $availableMaps->filter(function($map) use ($bannedMapIds) {
            return !in_array($map->id, $bannedMapIds);
        });

        if ($remainingMaps->count() === 1) {
            // Осталась одна карта - выбираем её
            $finalMap = $remainingMaps->first();
        } elseif ($remainingMaps->count() > 1) {
            // Если осталось несколько карт, выбираем случайную
            $finalMap = $remainingMaps->random();
        } else {
            // Если все карты забанены (не должно происходить), выбираем случайную из всех
            $finalMap = $availableMaps->random();
            Log::warning('Все карты забанены, выбрана случайная карта', [
                'match_id' => $this->id,
                'banned_count' => count($bannedMapIds),
                'total_maps' => $availableMaps->count()
            ]);
        }

        if ($finalMap) {
            $this->selected_map_id = $finalMap->id;
            $this->status = MatchStatus::LIVE->value;
            $this->save();

            Log::info('Выбрана финальная карта', [
                'match_id' => $this->id,
                'map_id' => $finalMap->id,
                'map_name' => $finalMap->name,
                'remaining_maps_count' => $remainingMaps->count(),
                'banned_maps_count' => count($bannedMapIds)
            ]);

            // Здесь можно добавить создание сервера
            $this->startGame();

            return $finalMap;
        }

        return null;
    }
    
    /**
     * Начинает игру (создание сервера)
     */
    public function startGame()
    {
        Log::info('Начинается игра', [
            'match_id' => $this->id,
            'selected_map_id' => $this->selected_map_id,
            'team1_id' => $this->team1_id,
            'team2_id' => $this->team2_id
        ]);
        
        // TODO: Здесь должна быть логика создания сервера
        // и отправки данных подключения капитанам команд
        
        // event(new GameStarted($this->id, $this->selected_map_id));
    }
}



