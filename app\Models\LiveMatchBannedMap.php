<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LiveMatchBannedMap extends Model
{
    protected $table = 'live_match_banned_maps';
    
    protected $fillable = [
        'match_id',
        'map_id',
        'team_id'
    ];
    
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
    
    /**
     * Связь с матчем
     */
    public function match()
    {
        return $this->belongsTo(LiveMatch::class, 'match_id');
    }
    
    /**
     * Связь с картой CS2
     */
    public function map()
    {
        return $this->belongsTo(Cs2Map::class, 'map_id');
    }
    
    /**
     * Связь с командой, которая забанила карту
     */
    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }
}
