<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LiveMatchReady extends Model
{
    protected $table = 'live_match_ready';
    
    // Отключаем timestamps, так как их нет в таблице
    public $timestamps = false;
    
    // Убираем составной первичный ключ, так как Laravel его не поддерживает
    // protected $primaryKey = ['match_id', 'player_id'];
    // public $incrementing = false;
    
    protected $fillable = [
        'match_id',
        'player_id',
        'is_ready'
    ];
    
    protected $casts = [
        'is_ready' => 'boolean'
    ];
    
    // Связь с матчем
    public function match()
    {
        return $this->belongsTo(LiveMatch::class, 'match_id');
    }
    
    // Связь с игроком
    public function player()
    {
        return $this->belongsTo(User::class, 'player_id');
    }
}
