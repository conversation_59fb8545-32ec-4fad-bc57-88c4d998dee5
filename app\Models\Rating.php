<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Rating extends Model
{
    use HasFactory;
    
    // Явно указываем имя таблицы
    protected $table = 'rating'; 
    
    protected $fillable = [
        'club_id',
        'player_id',
        'season_id',
        'game_id',
        'game_rating',
        'game_now',
    ];
    
    /**
     * Получить игрока, связанного с рейтингом.
     * Это альтернативный метод, который делает то же самое, что и user().
     */
    public function player(): BelongsTo
    {
        return $this->belongsTo(User::class, 'player_id', 'id');
    }
    
    /**
     * Получить игру, связанную с рейтингом.
     */
    public function game(): BelongsTo
    {
        return $this->belongsTo(Game::class, 'game_id', 'id');
    }
    
    /**
     * Получить клуб, связанный с рейтингом.
     */
    public function club(): BelongsTo
    {
        return $this->belongsTo(Club::class, 'club_id', 'club_id');
    }
    
    // /**
    //  * Получить сезон, связанный с рейтингом.
    //  */
    // public function season(): BelongsTo
    // {
    //     return $this->belongsTo(Season::class, 'season_id', 'id');
    // }
    
    /**
     * Переопределяем метод boot для добавления глобального скоупа,
     * который будет проверять существование связанного игрока.
     */
    // protected static function boot()
    // {
    //     //parent::boot();
        
    //     // // Добавляем глобальный скоуп для проверки существования игрока
    //     // static::addGlobalScope('hasPlayer', function ($query) {
    //     //     $query->whereExists(function ($query) {
    //     //         $query->select(\DB::raw(1))
    //     //               ->from('players')
    //     //               ->whereRaw('players.id = rating.player_id');
    //     //     });
    //     // });
        
    //     // Добавляем обработчики событий для синхронизации player_id и user_id
    //     // static::creating(function ($rating) {
    //     //     if ($rating->user_id && !$rating->player_id) {
    //     //         $rating->player_id = $rating->user_id;
    //     //     } elseif ($rating->player_id && !$rating->user_id) {
    //     //         $rating->user_id = $rating->player_id;
    //     //     }
    //     // });
        
    //     // static::updating(function ($rating) {
    //     //     if ($rating->isDirty('user_id') && !$rating->isDirty('player_id')) {
    //     //         $rating->player_id = $rating->user_id;
    //     //     } elseif ($rating->isDirty('player_id') && !$rating->isDirty('user_id')) {
    //     //         $rating->user_id = $rating->player_id;
    //     //     }
    //     // });
    // }
}
