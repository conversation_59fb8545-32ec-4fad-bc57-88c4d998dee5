<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Season extends Model
{
    use HasFactory;
    protected $table = 'events_season_rating';
    
    protected $fillable = [
        'event_id',
        'start_date',
        'player_id',
        'rating'
    ];

    public function event():BelongsTo
    {
        return $this->belongsTo(Event::class, 'event_id');
    }
    
    public function player():BelongsTo
    {
        return $this->belongsTo(User::class, 'player_id');
    }
}