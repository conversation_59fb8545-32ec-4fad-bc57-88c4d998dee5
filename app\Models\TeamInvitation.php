<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeamInvitation extends Model
{
    use HasFactory;

    protected $table = 'team_invitations';
    
    protected $fillable = [
        'team_id',
        'player_id',
        'invited_by',
        'status',
        'message'
    ];
    
    /**
     * Get the team that owns the invitation.
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }
    
    /**
     * Get the user that received the invitation.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'player_id');
    }
    
    /**
     * Get the user that sent the invitation.
     */
    public function inviter()
    {
        return $this->belongsTo(User::class, 'invited_by');
    }
}



