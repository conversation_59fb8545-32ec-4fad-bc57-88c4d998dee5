<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * Имя таблицы, связанной с моделью.
     *
     * @var string
     */
    protected $table = 'players';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_nick',
        'email',
        'password',
        'club_id',
        'client_id',
        'auth_token',
        'auth_token_from_date',
        'avatar',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    /**
     * Переопределение получения атрибута password.
     *
     * @param string $key
     * @return mixed
     */
    public function getAttribute($key)
    {
        if ($key === 'password') {
            $key = 'client_password';
        }

        return parent::getAttribute($key);
    }

    /**
     * Переопределение установки атрибута password.
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function setAttribute($key, $value)
    {
        if ($key === 'password') {
            $key = 'client_password';
        }

        parent::setAttribute($key, $value);
    }

    /**
     * Аксессор для получения значения пароля.
     *
     * @return string|null
     */
    public function getPasswordAttribute()
    {
        return $this->attributes['client_password'];
    }

    /**
     * Мутатор для установки значения пароля.
     *
     * @param string $value
     * @return void
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['client_password'] = $value;
    }

    /**
     * Получить матчи пользователя.
     */
    public function matches(): HasMany
    {
        return $this->hasMany(GameMatch::class, 'player_id');
    }

    /**
     * Получить результаты матчей пользователя.
     */
    public function matchResults(): HasMany
    {
        return $this->hasMany(MatchResult::class, 'player_id');
    }

    /**
     * Проверяет, является ли пользователь капитаном указанной команды
     *
     * @param int $teamId ID команды
     * @return bool
     */
    public function isCaptainOf($teamId)
    {
        return $this->teams()
            ->where('team_members.player_id', $this->id)
            ->where('team_id', $teamId)
            ->wherePivot('role', 'captain')
            ->exists();
    }

    /**
     * Проверяет, есть ли у пользователя команда для указанной игры
     *
     * @param int $gameId ID игры
     * @return bool
     */
    public function hasTeamForGame($gameId)
    {
        return $this->teams()
            ->whereHas('game', function($query) use ($gameId) {
                $query->where('id', $gameId);
            })
            ->exists();
    }

    /**
     * Получает команду пользователя для указанной игры
     *
     * @param int $gameId ID игры
     * @return \App\Models\Team|null
     */
    public function getTeamForGame($gameId)
    {
        // Проверяем, является ли пользователь капитаном команды для этой игры
        $captainTeam = \App\Models\Team::where('game_id', $gameId)
            ->where('captain_id', $this->id)
            ->first();
        
        if ($captainTeam) {
            return $captainTeam;
        }
        
        // Проверяем, является ли пользователь участником команды для этой игры
        $teamMember = \App\Models\TeamMember::whereHas('team', function($query) use ($gameId) {
            $query->where('game_id', $gameId);
        })->where('player_id', $this->id)->first();
        
        return $teamMember ? $teamMember->team : null;
    }

    /**
     * Получает членство пользователя в команде для указанной игры
     *
     * @param int $gameId ID игры
     * @return \App\Models\TeamMember|null
     */
    public function getTeamMembershipForGame($gameId)
    {
        return \App\Models\TeamMember::whereHas('team', function($query) use ($gameId) {
            $query->where('game_id', $gameId);
        })->where('player_id', $this->id)->first();
    }

    /**
     * Получает роль пользователя в указанной команде
     *
     * @param int $teamId ID команды
     * @return string|null
     */
    public function getRoleInTeam($teamId)
    {
        if ($this->isCaptainOf($teamId)) {
            return 'captain';
        }
        
        $teamMember = \App\Models\TeamMember::where('team_id', $teamId)
            ->where('player_id', $this->id)
            ->first();
        
        return $teamMember ? $teamMember->role : null;
    }

    /**
     * Команды, где пользователь является капитаном
     */
    public function captainedTeams()
    {
        return $this->hasMany(\App\Models\Team::class, 'captain_id');
    }

    /**
     * Команды, в которых состоит пользователь
     */
    public function teams()
    {
        return $this->belongsToMany(Team::class, 'team_members', 'player_id', 'team_id')
            ->withPivot('role')
            ->withTimestamps();
    }

    /**
     * Получить рейтинги пользователя.
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class, 'player_id');
    }

    /**
     * Получить рейтинг пользователя для конкретной игры.
     * 
     * @param int $gameId ID игры
     * @param int|null $clubId ID клуба (опционально)
     * @param int|null $seasonId ID сезона (опционально)
     * @return Rating|null
     */
    public function getRatingForGame($gameId, $clubId = null, $seasonId = null)
    {
        $query = $this->ratings()->where('game_id', $gameId);
        
        if ($clubId) {
            $query->where('club_id', $clubId);
        }
        
        if ($seasonId) {
            $query->where('season_id', $seasonId);
        }
        
        return $query->first();
    }

    /**
     * Получить или создать рейтинг пользователя для конкретной игры.
     * 
     * @param int $gameId ID игры
     * @param int $clubId ID клуба
     * @param int|null $seasonId ID сезона (опционально)
     * @param int $defaultRating Значение рейтинга по умолчанию
     * @return Rating
     */
    public function getOrCreateRatingForGame($gameId, $clubId, $seasonId = null, $defaultRating = 1000)
    {
        $rating = $this->getRatingForGame($gameId, $clubId, $seasonId);
        
        if (!$rating) {
            $rating = Rating::create([
                'player_id' => $this->id,
                'game_id' => $gameId,
                'club_id' => $clubId,
                'season_id' => $seasonId,
                'game_rating' => $defaultRating
            ]);
        }
        
        return $rating;
    }

    /**
     * Получить приглашения пользователя
     */
    public function invitations(): HasMany
    {
        return $this->hasMany(TeamInvitation::class, 'player_id');
    }

    /**
     * Получить заявки пользователя
     */
    public function requests(): HasMany
    {
        return $this->hasMany(JoinRequest::class, 'player_id');
    }

    /**
     * Получить активные приглашения пользователя
     */
    public function getPendingInvitations()
    {
        return $this->invitations()->where('status', 'pending')->with(['team', 'team.captain'])->get();
    }

    /**
     * Получить активные заявки пользователя
     */
    public function getPendingRequests()
    {
        return $this->requests()->where('status', 'pending')->with(['team', 'team.captain'])->get();
    }

    /**
     * Проверить, есть ли у пользователя активные приглашения
     */
    public function hasPendingInvitations(): bool
    {
        return $this->invitations()->where('status', 'pending')->exists();
    }

    /**
     * Проверить, есть ли у пользователя активные заявки
     */
    public function hasPendingRequests(): bool
    {
        return $this->requests()->where('status', 'pending')->exists();
    }

    /**
     * Проверить, есть ли у пользователя активные приглашения для конкретной игры
     */
    public function hasPendingInvitationsForGame(int $gameId): bool
    {
        return $this->invitations()
            ->where('status', 'pending')
            ->whereHas('team', function($query) use ($gameId) {
                $query->where('game_id', $gameId);
            })
            ->exists();
    }

    /**
     * Проверить, есть ли у пользователя активные заявки для конкретной игры
     */
    public function hasPendingRequestsForGame(int $gameId): bool
    {
        return $this->requests()
            ->where('status', 'pending')
            ->whereHas('team', function($query) use ($gameId) {
                $query->where('game_id', $gameId);
            })
            ->exists();
    }
}


