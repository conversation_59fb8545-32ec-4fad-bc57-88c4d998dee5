<?php

namespace App\Services;

use App\Models\Team;
use App\Models\LiveMatch;
use App\Models\LiveMatchReady;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MatchmakingService
{
    /**
     * Finds a match for the given team.
     *
     * @param Team $team
     * @return LiveMatch|null
     */
    public function findMatch(Team $team)
    {
        Log::info('Вызов MatchmakingService::findMatch()');
        try {
            DB::beginTransaction();
    
            // Проверяем, есть ли вообще команды в игре
            $availableTeamsQuery = Team::where('id', '!=', $team->id)
                ->where('game_id', $team->game_id);
    
            $availableTeamsCount = $availableTeamsQuery->count();
            Log::info('Доступные команды для матча: ' . $availableTeamsCount);
    
            if ($availableTeamsCount === 0) {
                throw new \Exception('Нет доступных команд для матча в этой игре. Возможно, никто не ищет матч в данный момент.');
            }
    
            // Случайный выбор команды-соперника
            $opponentTeam = $availableTeamsQuery->inRandomOrder()->first();
    
            if (!$opponentTeam) {
                Log::warning('Не найдена команда-соперник для команды: ' . $team->id);
                throw new \Exception('Не найдена команда-соперник. Попробуйте повторить поиск позже.');
            }
    
            // Создаем матч с явным указанием current_voter
            $match = new LiveMatch();
            $match->team1_id = $team->id;
            $match->team2_id = $opponentTeam->id;
            $match->status = MatchStatus::READY_CHECK->value;
            $match->current_voting_team = $team->id; // Устанавливаем команду 1 как голосующую первой
            $match->save();
    
            // Получаем капитанов команд
            $team1Captain = \App\Models\TeamMember::where('team_id', $team->id)
                ->where('role', 'captain')
                ->first();
    
            $team2Captain = \App\Models\TeamMember::where('team_id', $opponentTeam->id)
                ->where('role', 'captain')
                ->first();
    
            if (!$team1Captain || !$team2Captain) {
                throw new \Exception('Не найдены капитаны команд. Убедитесь, что у обеих команд есть капитаны.');
            }
    
            // Создаем записи о готовности только если их еще нет
            DB::table('live_match_ready')
                ->updateOrInsert(
                    [
                        'match_id' => $match->id,
                        'player_id' => $team1Captain->player_id
                    ],
                    [
                        'is_ready' => false
                    ]
                );
    
            DB::table('live_match_ready')
                ->updateOrInsert(
                    [
                        'match_id' => $match->id,
                        'player_id' => $team2Captain->player_id
                    ],
                    [
                        'is_ready' => false
                    ]
                );
    
            DB::commit();
    
            return $match;
    
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Ошибка при создании матча: ' . $e->getMessage(), [
                'team_id' => $team->id,
                'game_id' => $team->game_id ?? null,
                'available_teams' => $availableTeamsCount ?? 0
            ]);
            throw $e;
        }
    }
}