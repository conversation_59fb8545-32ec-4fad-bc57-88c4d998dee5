<?php

namespace App\Services;

use App\Models\User;
use App\Models\TeamInvitation;
use App\Models\JoinRequest;
use Illuminate\Support\Collection;

class PlayerService
{
    /**
     * Получение списка игроков для приглашения
     */
    public function getPlayersForInvite(int $excludeUserId): Collection
    {
        return User::where('id', '!=', $excludeUserId)
            ->select('id', 'client_nick')
            ->orderBy('client_nick')
            ->get()
            ->map(function($player) {
                return [
                    'id' => $player->id,
                    'name' => $player->client_nick
                ];
            });
    }

    /**
     * Проверка, является ли пользователь капитаном команды
     */
    public function isCaptainOf(int $userId, int $teamId): bool
    {
        return User::find($userId)->isCaptainOf($teamId);
    }

    /**
     * Проверка, есть ли у пользователя команда для конкретной игры
     */
    public function hasTeamForGame(int $userId, int $gameId): bool
    {
        return User::find($userId)->hasTeamForGame($gameId);
    }

    /**
     * Получение списка приглашений игрока
     */
    public function getPlayerInvitations(int $userId): Collection
    {
        return TeamInvitation::where('player_id', $userId)
            ->where('status', 'pending')
            ->with(['team', 'team.captain'])
            ->get();
    }

    /**
     * Получение списка заявок игрока
     */
    public function getPlayerRequests(int $userId): Collection
    {
        return JoinRequest::where('player_id', $userId)
            ->where('status', 'pending')
            ->with(['team', 'team.captain'])
            ->get();
    }

    /**
     * Проверка, есть ли у игрока активные приглашения
     */
    public function hasActiveInvitations(int $userId): bool
    {
        return TeamInvitation::where('player_id', $userId)
            ->where('status', 'pending')
            ->exists();
    }

    /**
     * Проверка, есть ли у игрока активные заявки
     */
    public function hasActiveRequests(int $userId): bool
    {
        return JoinRequest::where('player_id', $userId)
            ->where('status', 'pending')
            ->exists();
    }
} 