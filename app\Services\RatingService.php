<?php

namespace App\Services;

use App\Models\GameMatch;
use App\Models\Rating;
use App\Models\Season;
use Carbon\Carbon;
use Laravel\Pennant\Feature;
use Illuminate\Support\Facades\Cache;

class RatingService
{
    /**
     * Создает новый экземпляр класса.
     */
    public function __construct()
    {
        //
    }

    /**
     * Расчет рейтинга с учетом замедления
     */
    public function calculateRating($playerId, $gameType, $score)
    {
        // Используем кэширование
        return Cache::remember("rating_{$playerId}_{$gameType}", 3600, function() use ($playerId, $gameType, $score) {
            // Проверяем, включена ли функция замедления
            if (!Feature::active('rating-slowdown')) {
                return $score;
            }
            
            // Логика замедления роста рейтинга
            // Получаем количество матчей за последние 24 часа
            $matchesCount = GameMatch::where('user_id', $playerId)
                ->where('game_type', $gameType)
                ->where('created_at', '>=', now()->subDay())
                ->count();
            
            // Применяем замедление
            if ($matchesCount > 20) {
                $score = round($score * 0.25); // 25% от обычного
            } elseif ($matchesCount > 15) {
                $score = round($score * 0.5); // 50% от обычного
            } elseif ($matchesCount > 10) {
                $score = round($score * 0.75); // 75% от обычного
            } elseif ($matchesCount > 5) {
                $score = round($score * 0.9); // 90% от обычного
            }
            
            return $score;
        });
    }
    
    /**
     * Получение данных о матче из внешнего API (для Dota 2)
     */
    public function getDota2MatchData($matchId)
    {
        // Запрос к API OpenDota
        $response = file_get_contents("https://api.opendota.com/api/matches/{$matchId}");
        return json_decode($response, true);
    }
    
    /**
     * Получение данных о матче из внешнего API (для PUBG)
     */
    public function getPubgMatchData($matchId)
    {
        // Запрос к API PUBG
        $ch = curl_init("https://api.pubg.com/shards/steam/matches/{$matchId}");
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('accept: application/vnd.api+json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_HEADER, false);
        $response = curl_exec($ch);
        curl_close($ch);
        
        return json_decode($response, true);
    }

    /**
     * Расчет рейтинга для CS2
     */
    public function calculateCS2Rating($matchData)
    {
        // Проверяем, включена ли улучшенная система рейтинга
        $enhancedRating = Feature::active('enhanced-rating');
        
        // Базовые очки за победу/поражение
        $baseScore = $matchData['victory'] ? 30 : 10;
        
        // Бонус за KDA
        $kills = $matchData['details']['kills'] ?? 0;
        $deaths = max(1, $matchData['details']['deaths'] ?? 1); // Избегаем деления на ноль
        $assists = $matchData['details']['assists'] ?? 0;
        
        // Расчет KDA
        $kda = ($kills + $assists) / $deaths;
        $kdaBonus = min(20, round($kda * 5)); // Максимум 20 очков за KDA
        
        // Бонус за раунды (для CS2)
        $roundsWon = $matchData['details']['rounds_won'] ?? 0;
        $roundsBonus = min(15, $roundsWon); // Максимум 15 очков за выигранные раунды
        
        // Бонус за хедшоты (только для улучшенной системы)
        $headshotBonus = 0;
        if ($enhancedRating && isset($matchData['details']['headshots'])) {
            $headshots = $matchData['details']['headshots'];
            $headshotBonus = min(10, $headshots * 2); // 2 очка за хедшот, максимум 10
        }
        
        $totalScore = $baseScore + $kdaBonus + $roundsBonus + $headshotBonus;
        
        // Применяем замедление роста рейтинга
        return $this->calculateRating($matchData['player_id'], 'cs2', $totalScore);
    }
    
    /**
     * Расчет рейтинга для Dota 2
     */
    public function calculateDota2Rating($matchData)
    {
        // Проверяем, включена ли улучшенная система рейтинга
        $enhancedRating = Feature::active('enhanced-rating');
        
        // Базовые очки за победу/поражение
        $baseScore = $matchData['victory'] ? 25 : 5;
        
        // Бонус за KDA
        $kills = $matchData['details']['kills'] ?? 0;
        $deaths = max(1, $matchData['details']['deaths'] ?? 1); // Избегаем деления на ноль
        $assists = $matchData['details']['assists'] ?? 0;
        
        // Расчет KDA
        $kda = ($kills + $assists) / $deaths;
        $kdaBonus = min(15, round($kda * 3)); // Максимум 15 очков за KDA
        
        // Бонус за GPM и XPM
        $gpm = $matchData['details']['gpm'] ?? 0;
        $xpm = $matchData['details']['xpm'] ?? 0;
        
        $gpmBonus = min(10, round($gpm / 100)); // Максимум 10 очков за GPM
        $xpmBonus = min(10, round($xpm / 100)); // Максимум 10 очков за XPM
        
        // Бонус за длительность матча (только для улучшенной системы)
        $durationBonus = 0;
        if ($enhancedRating && isset($matchData['details']['duration'])) {
            $duration = $matchData['details']['duration']; // в минутах
            // Более длинные матчи дают больше очков
            $durationBonus = min(10, round($duration / 10)); // 1 очко за каждые 10 минут, максимум 10
        }
        
        $totalScore = $baseScore + $kdaBonus + $gpmBonus + $xpmBonus + $durationBonus;
        
        // Применяем замедление роста рейтинга
        return $this->calculateRating($matchData['player_id'], 'dota2', $totalScore);
    }
    
    /**
     * Расчет рейтинга для PUBG
     */
    public function calculatePubgRating($matchData)
    {
        // Проверяем, включена ли улучшенная система рейтинга
        $enhancedRating = Feature::active('enhanced-rating');
        
        // Базовые очки за выживание
        $survivalTime = $matchData['details']['timeSurvived'] ?? 0;
        $survivalScore = min(40, round($survivalTime / 60)); // 1 очко за минуту, максимум 40
        
        // Бонус за убийства
        $kills = $matchData['details']['kills'] ?? 0;
        $killScore = $kills * 5; // 5 очков за каждое убийство
        
        // Бонус за место
        $place = $matchData['details']['winPlace'] ?? 99;
        $totalTeams = $matchData['details']['totalTeams'] ?? 25;
        
        // Формула: чем выше место, тем больше очков
        // 1 место = 50 очков, последнее место = 0 очков
        $placeScore = $place == 1 ? 50 : max(0, round(50 * (1 - ($place - 1) / ($totalTeams - 1))));
        
        // Бонус за урон (только для улучшенной системы)
        $damageBonus = 0;
        if ($enhancedRating && isset($matchData['details']['damageDealt'])) {
            $damage = $matchData['details']['damageDealt'];
            $damageBonus = min(15, round($damage / 100)); // 1 очко за каждые 100 урона, максимум 15
        }
        
        $totalScore = $survivalScore + $killScore + $placeScore + $damageBonus;
        
        // Применяем замедление роста рейтинга
        return $this->calculateRating($matchData['player_id'], 'pubg', $totalScore);
    }
    
    /**
     * Обновление рейтинга игрока
     */
    public function updatePlayerRating($userId, $gameId, $clubId, $addScore)
    {
        // Получаем текущий сезон
        $currentSeason = Season::where('is_active', true)->first();
        $seasonId = $currentSeason ? $currentSeason->id : null;
        
        // Находим или создаем запись рейтинга, используя player_id вместо user_id
        $rating = Rating::firstOrCreate(
            [
                'player_id' => $userId, // Изменено с user_id на player_id
                'game_id' => $gameId,
                'club_id' => $clubId,
                'season_id' => $seasonId
            ],
            ['game_rating' => 1000] // Начальный рейтинг
        );
        
        // Обновляем рейтинг
        $rating->game_rating += $addScore;
        $rating->save();
        
        return $rating;
    }
    
    /**
     * Сброс рейтинга для нового сезона
     */
    public function resetSeasonRatings($newSeasonId)
    {
        // Проверяем, включена ли функция сезонного сброса
        if (!Feature::active('seasonal-reset')) {
            return false;
        }
        
        // Получаем все рейтинги предыдущего сезона
        $previousSeason = Season::where('is_active', false)
            ->orderBy('end_date', 'desc')
            ->first();
            
        if (!$previousSeason) {
            return false;
        }
        
        $previousRatings = Rating::where('season_id', $previousSeason->id)->get();
        
        foreach ($previousRatings as $prevRating) {
            // Создаем новую запись рейтинга для нового сезона
            // Начальный рейтинг = 1000 + 10% от предыдущего рейтинга
            $initialRating = 1000 + round(($prevRating->game_rating - 1000) * 0.1);
            
            Rating::firstOrCreate(
                [
                    'user_id' => $prevRating->user_id,
                    'game_id' => $prevRating->game_id,
                    'club_id' => $prevRating->club_id,
                    'season_id' => $newSeasonId
                ],
                ['game_rating' => $initialRating]
            );
        }
        
        return true;
    }
}




