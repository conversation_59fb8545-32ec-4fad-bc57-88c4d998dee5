<?php

namespace App\Services;

use App\Models\Team;
use App\Models\TeamMember;
use App\Models\TeamInvitation;
use App\Models\JoinRequest;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class TeamService
{
    /**
     * Создание новой команды
     */
    public function createTeam(string $name, string $description, int $gameId, int $captainId): Team
    {
        $team = Team::create([
            'name' => $name,
            'description' => $description,
            'game_id' => $gameId,
            'captain_id' => $captainId,
            'rating' => 0
        ]);

        TeamMember::create([
            'team_id' => $team->id,
            'player_id' => $captainId,
            'role' => 'captain'
        ]);

        return $team;
    }

    /**
     * Получение команд пользователя для конкретной игры
     */
    public function getUserTeamsForGame(int $userId, int $gameId)
    {
        return Team::where('game_id', $gameId)
            ->where(function($query) use ($userId) {
                $query->whereHas('members', function($q) use ($userId) {
                    $q->where('player_id', $userId);
                })
                ->orWhere('captain_id', $userId);
            })
            ->with(['captain', 'game'])
            ->get();
    }

    /**
     * Загрузка участников команды
     */
    public function loadTeamMembers(int $teamId)
    {
        return TeamMember::where('team_id', $teamId)
            ->with('user')
            ->get();
    }

    /**
     * Загрузка статистики команды
     */
    public function loadTeamStats(int $teamId)
    {
        // TODO: Реализовать реальную статистику
        return [
            'matches' => 0,
            'wins' => 0,
            'losses' => 0,
            'draws' => 0
        ];
    }

    /**
     * Загрузка ожидающих приглашений
     */
    public function loadPendingInvitations(int $teamId)
    {
        return TeamInvitation::where('team_id', $teamId)
            ->where('status', 'pending')
            ->with('user')
            ->get();
    }

    /**
     * Загрузка ожидающих заявок
     */
    public function loadPendingRequests(int $teamId)
    {
        return JoinRequest::where('team_id', $teamId)
            ->where('status', 'pending')
            ->with('user')
            ->get();
    }

    /**
     * Отправка приглашения игроку
     */
    public function sendInvitation(int $teamId, int $playerId, int $invitedBy): void
    {
        TeamInvitation::create([
            'team_id' => $teamId,
            'player_id' => $playerId,
            'invited_by' => $invitedBy,
            'status' => 'pending'
        ]);
    }

    /**
     * Принятие заявки на вступление
     */
    public function acceptRequest(int $requestId): void
    {
        $request = JoinRequest::findOrFail($requestId);
        $request->status = 'accepted';
        $request->save();

        TeamMember::create([
            'team_id' => $request->team_id,
            'player_id' => $request->player_id,
            'role' => 'member'
        ]);
    }

    /**
     * Отклонение заявки на вступление
     */
    public function rejectRequest(int $requestId): void
    {
        $request = JoinRequest::findOrFail($requestId);
        $request->status = 'rejected';
        $request->save();
    }

    /**
     * Отмена приглашения
     */
    public function cancelInvitation(int $invitationId): void
    {
        TeamInvitation::findOrFail($invitationId)->delete();
    }

    /**
     * Исключение участника из команды
     */
    public function kickMember(int $teamId, int $userId): void
    {
        TeamMember::where('team_id', $teamId)
            ->where('player_id', $userId)
            ->delete();

        JoinRequest::where('team_id', $teamId)
            ->where('player_id', $userId)
            ->where('status', 'accepted')
            ->delete();
    }

    /**
     * Расформирование команды
     */
    public function disbandTeam(int $teamId): void
    {
        TeamMember::where('team_id', $teamId)->delete();
        Team::findOrFail($teamId)->delete();
    }
} 