<?php

namespace Database\Seeders;

use App\Models\GameMatch;
use App\Models\MatchResult;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Carbon;

class MatchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Получаем пользователя или создаем нового, если нет ни одного
        $user = User::first() ?? User::factory()->create();
        
        // Создаем тестовые матчи Dota 2
        for ($i = 1; $i <= 5; $i++) {
            $match = GameMatch::create([
                'game_type' => 'dota2',
                'session_id' => 'test-dota2-' . $i,
                'match_id' => '1000' . $i,
                'score' => rand(50, 100),
                'match_score' => rand(10, 30) . ':' . rand(10, 30),
                'date_scan' => Carbon::now()->subHours(rand(1, 48)),
                'col_scan' => 1,
                'user_id' => $user->id,
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
            
            // Создаем результат матча
            MatchResult::create([
                'match_id' => $match->id,
                'victory' => rand(0, 1),
                'round' => 1,
                'add_score' => $match->score,
                'details' => json_encode(['test' => 'data']),
            ]);
        }
        
        // Создаем тестовые матчи PUBG
        for ($i = 1; $i <= 5; $i++) {
            $match = GameMatch::create([
                'game_type' => 'pubg',
                'session_id' => 'test-pubg-' . $i,
                'match_id' => '2000' . $i,
                'score' => rand(40, 90),
                'match_score' => 'Place: ' . rand(1, 20),
                'date_scan' => Carbon::now()->subHours(rand(1, 48)),
                'col_scan' => 1,
                'user_id' => $user->id,
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
            
            // Создаем результат матча
            MatchResult::create([
                'match_id' => $match->id,
                'victory' => $match->score > 70 ? 1 : 0, // Победа, если счет больше 70
                'round' => 1,
                'add_score' => $match->score,
                'details' => json_encode(['test' => 'data']),
            ]);
        }
        
        // Создаем тестовые матчи CS2
        for ($i = 1; $i <= 5; $i++) {
            $match = GameMatch::create([
                'game_type' => 'cs2',
                'session_id' => 'test-cs2-' . $i,
                'match_id' => '3000' . $i,
                'score' => rand(45, 95),
                'match_score' => rand(5, 16) . ':' . rand(0, 15),
                'date_scan' => Carbon::now()->subHours(rand(1, 48)),
                'col_scan' => 1,
                'user_id' => $user->id,
                'created_at' => Carbon::now()->subDays(rand(1, 30)),
            ]);
            
            // Создаем результат матча с деталями, специфичными для CS2
            MatchResult::create([
                'match_id' => $match->id,
                'victory' => rand(0, 1),
                'round' => rand(16, 30), // Общее количество раундов
                'add_score' => $match->score,
                'details' => json_encode([
                    'kills' => rand(5, 30),
                    'deaths' => rand(5, 20),
                    'assists' => rand(2, 15),
                    'headshots' => rand(3, 15),
                    'rounds_won' => rand(5, 16),
                    'map' => ['de_dust2', 'de_mirage', 'de_inferno', 'de_nuke', 'de_overpass'][rand(0, 4)],
                ]),
            ]);
        }
    }
}
