1. **Поиск игры:**
    *   Капитан каждой команды инициирует поиск игры.
    *   Система подбирает две команды на основе рейтинга, пинга и других критериев (например, регион).
2.  **Подтверждение игры:**
    *   Система отправляет уведомление капитанам обеих команд о найденной игре.
    *   Каждый капитан должен подтвердить готовность к игре в течение определенного времени (например, 30 секунд).
    *   Если один из капитанов не подтверждает игру вовремя, поиск начинается заново для обеих команд.
3.  **Выбор карты (бан/пик):**
    *   Обе команды по очереди исключают карты из общего списка доступных карт.
    *   Процесс исключения (бана) карт продолжается до тех пор, пока не останется одна карта.
    *   Первой исключает команда, у которой выше рейтинг (или случайно, если рейтинги равны).
    *   Каждый капитан выбирает карту для исключения в течение определенного времени (например, 15 секунд).
    *   Если капитан не выбирает карту вовремя, система автоматически исключает случайную карту.
4.  **Начало игры:**
    *   После выбора карты начинается игра.
    *   Система автоматически создает сервер с выбранной картой и отправляет данные для подключения капитанам команд.

5. **Реализовать систему таймеров через Events и Jobs**
    *   Добавить автоматические действия при истечении таймеров
    *   Улучшить систему уведомлений между командами
    *   Добавить валидацию на все действия
    *   Реализовать очередность банов согласно алгоритму
    *   Добавить обработку крайних случаев (отключение игрока, проблемы с сетью)

    1.  **Управление модальным окном Alpine.js:**
    * Убран класс `fade` из модального окна, так как `x-show` и `x-transition` Alpine.js уже обеспечивают эффект появления/исчезновения. `modal` остается для базовых стилей Bootstrap.
    * В `toggleAcceptModal` теперь можно передавать `matchData`, если вы захотите отобразить что-то конкретное о матче в модальном окне.
    * Проверка `if (element && element.__x)` заменена на более надежный способ общения между JavaScript и Livewire/Alpine.

2.  **Обработка события `match.accepted`:**
    * Вместо прямого манипулирования Alpine.js `$data` из внешнего JS, я предложил использовать `Livewire.dispatchSelf('showMatchAcceptModal', { matchId: e.matchId });`. Это более "Livewire-way" общения: вы отправляете событие *текущему* Livewire-компоненту.
    * Затем, в Blade (или отдельном Alpine.js скрипте, который прослушивает Livewire события), добавляется `Livewire.on('showMatchAcceptModal', (event) => { ... });`. Этот слушатель будет реагировать на событие, отправленное из Echo, и вызовет `toggleAcceptModal` Alpine.js. Это разделяет ответственность: Echo -> Livewire -> Alpine.js.

3.  **Безопасность `$currentVotingTeam`:**
    * Добавлен более строгий контроль `(int)$currentVotingTeam->id === (int)$teamId` для обеспечения, что сравнение происходит между числами и предотвращения ошибок, если `$currentVotingTeam` окажется `null`.

4.  **Улучшение Blade-синтаксиса:**
    * Использование оператора нулевого слияния `??` для более лаконичного доступа к потенциально отсутствующим свойствам, например, `{{ $opponents['team']->name ?? 'Неизвестная команда' }}`.
    * Определена `$opponentTeamId` переменная для улучшения читаемости.

5.  **Логирование и отладка:**
    
    **Бан карт**    
    * Определить, какая команда должна банить карту следующей.
    * Хранить информацию о том, какая команда банила карту последней.
    * Проверять, что текущая команда имеет право банить карту.
    * Передавать ход бана другой команде после каждого бана.

    Теперь, когда поле current_voting_team добавлено в модель GameMatch, необходимо обновить компонент app/Livewire/MatchSearch.php, чтобы он учитывал это поле при бане карт.

Необходимо:

Получать значение поля current_voting_team из модели GameMatch.
Проверять, что текущая команда имеет право банить карту (ID команды совпадает со значением поля current_voting_team).
Обновлять значение поля current_voting_team после каждого бана, чтобы ход переходил к другой команде.

**Проверки:**
    - Существование матча
    - Очередь команды (current_voting_team)
    - Валидация массивов

    // Действия:
    - Добавление карты в список забаненных
    - Удаление карты из доступных
    - Смена очереди голосования на другую команду
    - Обновление статуса голосующей команды 

**Процесс бана карт:**
1.Команды по очереди банят карты
2.После каждого бана:
    *Карта добавляется в bannedMaps
    *Удаляется из availableMaps
    *Очередь переходит к другой команде
3.Процесс продолжается до достижения maxBans или выбора финальной карты

**Возможные улучшения:**
1.Добавить валидацию максимального количества банов
2.Добавить проверку статуса матча
3.Реализовать выбор финальной карты
4.Добавить таймер на бан карты
5.Реализовать автоматический бан при истечении времени           