﻿CREATE TABLE rgtournament.team_invitations (
  id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  team_id int(11) NOT NULL,
  player_id int(11) NOT NULL,
  invited_by int(11) NOT NULL,
  status enum ('pending', 'accepted', 'rejected') NOT NULL DEFAULT 'pending',
  created_at timestamp NULL DEFAULT NULL,
  updated_at timestamp NULL DEFAULT NULL,
  PRIMARY KEY (id)
)
ENGINE = INNODB,
CHARACTER SET utf8mb4,
COLLATE utf8mb4_unicode_ci;

ALTER TABLE rgtournament.team_invitations
ADD UNIQUE INDEX team_invitations_team_id_player_id_status_unique (team_id, player_id, status);

ALTER TABLE rgtournament.team_invitations
ADD CONSTRAINT team_invitations_invited_by_foreign FOREIGN KEY (invited_by)
REFERENCES rgtournament.players (id) ON DELETE CASCADE;

ALTER TABLE rgtournament.team_invitations
ADD CONSTRAINT team_invitations_team_id_foreign FOREIGN KEY (team_id)
REFERENCES rgtournament.teams (id) ON DELETE CASCADE;

ALTER TABLE rgtournament.team_invitations
ADD CONSTRAINT team_invitations_player_id_foreign FOREIGN KEY (player_id)
REFERENCES rgtournament.players (id) ON DELETE CASCADE;