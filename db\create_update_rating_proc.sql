DELIMITER $$

CREATE PROCEDURE UpdateRatingTable()
BEGIN
    -- Проверка и добавление столбца game_id
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'rating'
          AND COLUMN_NAME = 'game_id'
    ) THEN
        ALTER TABLE rgtournament.rating
        ADD COLUMN game_id INT NOT NULL AFTER id;
    END IF;

    -- Проверка и добавление столбца club_id
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'rating'
          AND COLUMN_NAME = 'club_id'
    ) THEN
        ALTER TABLE rgtournament.rating
        ADD COLUMN club_id INT NULL AFTER game_id;
    END IF;

    -- Проверка и добавление столбца player_id
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'rating'
          AND COLUMN_NAME = 'player_id'
    ) THEN
        ALTER TABLE rgtournament.rating
        ADD COLUMN player_id INT NOT NULL AFTER club_id;
    END IF;

    -- Проверка и добавление столбца season_id
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'rating'
          AND COLUMN_NAME = 'season_id'
    ) THEN
        ALTER TABLE rgtournament.rating
        ADD COLUMN season_id INT NULL AFTER player_id;
    END IF;

    -- Проверка и добавление столбца game_rating
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'rating'
          AND COLUMN_NAME = 'game_rating'
    ) THEN
        ALTER TABLE rgtournament.rating
        ADD COLUMN game_rating INT NOT NULL DEFAULT 1000 AFTER season_id;
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL UpdateRatingTable();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS UpdateRatingTable;
