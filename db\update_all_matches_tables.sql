DELIMITER //

CREATE PROCEDURE update_matches_indexes()
BEGIN
    -- Проверяем и удаляем индексы для matches
    IF EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches' 
        AND index_name = 'idx_club_date'
    ) THEN
        ALTER TABLE matches DROP INDEX idx_club_date;
    END IF;

    IF EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches' 
        AND index_name = 'idx_club_id'
    ) THEN
        ALTER TABLE matches DROP INDEX idx_club_id;
    END IF;

    IF EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches' 
        AND index_name = 'idx_date'
    ) THEN
        ALTER TABLE matches DROP INDEX idx_date;
    END IF;

    IF EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches' 
        AND index_name = 'idx_victory'
    ) THEN
        ALTER TABLE matches DROP INDEX idx_victory;
    END IF;

    -- Добавляем индексы для matches_dota2
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches_dota2' 
        AND index_name = 'idx_player_date'
    ) THEN
        ALTER TABLE matches_dota2 ADD INDEX idx_player_date (player_id, date);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches_dota2' 
        AND index_name = 'idx_player_id'
    ) THEN
        ALTER TABLE matches_dota2 ADD INDEX idx_player_id (player_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches_dota2' 
        AND index_name = 'idx_player_date_victory'
    ) THEN
        ALTER TABLE matches_dota2 ADD INDEX idx_player_date_victory (player_id, date, victory);
    END IF;

    -- Добавляем индексы для matches_pubg
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches_pubg' 
        AND index_name = 'idx_player_date'
    ) THEN
        ALTER TABLE matches_pubg ADD INDEX idx_player_date (player_id, date);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches_pubg' 
        AND index_name = 'idx_player_id'
    ) THEN
        ALTER TABLE matches_pubg ADD INDEX idx_player_id (player_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.statistics 
        WHERE table_schema = 'rgtournament' 
        AND table_name = 'matches_pubg' 
        AND index_name = 'idx_player_date_victory'
    ) THEN
        ALTER TABLE matches_pubg ADD INDEX idx_player_date_victory (player_id, date, victory);
    END IF;
END //

DELIMITER ;

-- Выполняем процедуру
CALL update_matches_indexes();

-- Удаляем процедуру после использования
DROP PROCEDURE IF EXISTS update_matches_indexes;