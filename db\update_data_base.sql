﻿DELIMITER $$

CREATE PROCEDURE CreateMissingTables()
BEGIN
    -- Проверка и создание таблицы rgtournament.cache
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'cache'
    ) THEN
        CREATE TABLE rgtournament.cache (
            `key` varchar(255) NOT NULL,
            value mediumtext NOT NULL,
            expiration int(11) NOT NULL,
            PRIMARY KEY (`key`)
        )
        ENGINE = INNODB,
        CHARACTER SET utf8mb4,
        COLLATE utf8mb4_unicode_ci;
    END IF;

    -- Проверка и создание таблицы rgtournament.cache_locks
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'cache_locks'
    ) THEN
        CREATE TABLE rgtournament.cache_locks (
            `key` varchar(255) NOT NULL,
            owner varchar(255) NOT NULL,
            expiration int(11) NOT NULL,
            PRIMARY KEY (`key`)
        )
        ENGINE = INNODB,
        CHARACTER SET utf8mb4,
        COLLATE utf8mb4_unicode_ci;
    END IF;

    -- Проверка и создание таблицы rgtournament.jobs
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'jobs'
    ) THEN
        CREATE TABLE rgtournament.jobs (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            queue varchar(255) NOT NULL,
            payload longtext NOT NULL,
            attempts tinyint(3) UNSIGNED NOT NULL,
            reserved_at int(10) UNSIGNED DEFAULT NULL,
            available_at int(10) UNSIGNED NOT NULL,
            created_at int(10) UNSIGNED NOT NULL,
            PRIMARY KEY (id)
        )
        ENGINE = INNODB,
        CHARACTER SET utf8mb4,
        COLLATE utf8mb4_unicode_ci;

        ALTER TABLE rgtournament.jobs
        ADD INDEX jobs_queue_index (queue);
    END IF;

    -- Проверка и создание таблицы rgtournament.sessions
    IF NOT EXISTS (
        SELECT 1
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'sessions'
    ) THEN
        CREATE TABLE rgtournament.sessions (
            id varchar(255) NOT NULL,
            user_id bigint(20) UNSIGNED DEFAULT NULL,
            ip_address varchar(45) DEFAULT NULL,
            user_agent text DEFAULT NULL,
            payload longtext NOT NULL,
            last_activity int(11) NOT NULL,
            PRIMARY KEY (id)
        )
        ENGINE = INNODB,
        AVG_ROW_LENGTH = 16384,
        CHARACTER SET utf8mb4,
        COLLATE utf8mb4_unicode_ci;

        ALTER TABLE rgtournament.sessions
        ADD INDEX sessions_last_activity_index (last_activity);

        ALTER TABLE rgtournament.sessions
        ADD INDEX sessions_user_id_index (user_id);
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры для создания таблиц (если они не существуют)
CALL CreateMissingTables();

-- Удаление процедуры после выполнения (опционально)
DROP PROCEDURE IF EXISTS CreateMissingTables;