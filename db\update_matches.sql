﻿DELIMITER $$

DROP PROCEDURE IF EXISTS `upgrade_matches_table`$$

CREATE PROCEDURE `upgrade_matches_table`()
BEGIN
    -- Добавление поля created_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'matches'
          AND COLUMN_NAME = 'created_at'
    ) THEN
        ALTER TABLE rgtournament.matches
        ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL;
    END IF;

    -- Добавление поля updated_at, если оно отсутствует
    IF NOT EXISTS (
        SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = 'rgtournament'
          AND TABLE_NAME = 'matches'
          AND COLUMN_NAME = 'updated_at'
    ) THEN
        ALTER TABLE rgtournament.matches
        ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL;
    END IF;
END$$

DELIMITER ;

-- Вызов процедуры
CALL upgrade_matches_table();

-- Удаление процедуры после выполнения
DROP PROCEDURE IF EXISTS `upgrade_matches_table`;