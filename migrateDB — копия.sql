﻿-- Установка имени базы данных как переменной
SET @db_name = 'rgtournament';

-- Использование базы данных
USE rgtournament;

-- Настройка кодировки для базы данных (напрямую, без подготовленных выражений)
ALTER DATABASE rgtournament 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- Для таблицы players
ALTER TABLE players 
    CONVERT TO CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- Проверка и добавление email
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'players' AND column_name = 'email');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE players ADD COLUMN email VARCHAR(255) NOT NULL DEFAULT "" AFTER client_nick', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Проверка и добавление password
/*SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'players' AND column_name = 'password');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE players ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT "" AFTER client_password', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;*/

-- Аналогично для остальных столбцов
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'players' AND column_name = 'remember_token');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE players ADD COLUMN remember_token VARCHAR(100) DEFAULT NULL', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'players' AND column_name = 'created_at');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE players ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'players' AND column_name = 'updated_at');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE players ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Остальные таблицы обрабатываются аналогично
-- Пример для teams
-- Проверяем существование таблицы teams
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'teams' 
                     AND table_schema = @db_name);

-- Создаем процедуру для добавления столбцов в таблицу teams
DROP PROCEDURE IF EXISTS add_timestamp_columns_to_teams;
DELIMITER //
CREATE PROCEDURE add_timestamp_columns_to_teams()
BEGIN
    -- Если таблица существует, проверяем и добавляем столбцы
    IF @table_exists > 0 THEN
        -- Проверка и добавление created_at в таблицу teams
        SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                           WHERE table_name = 'teams' 
                           AND table_schema = @db_name
                           AND column_name = 'created_at');
        
        IF @col_exists = 0 THEN
            ALTER TABLE teams ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL;
        END IF;
        
        -- Проверка и добавление updated_at в таблицу teams
        SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                           WHERE table_name = 'teams' 
                           AND table_schema = @db_name
                           AND column_name = 'updated_at');
        
        IF @col_exists = 0 THEN
            ALTER TABLE teams ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL;
        END IF;
    END IF;
END //
DELIMITER ;

-- Вызываем процедуру
CALL add_timestamp_columns_to_teams();
DROP PROCEDURE IF EXISTS add_timestamp_columns_to_teams;

-- Создание таблиц с проверкой существования
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    email VARCHAR(255) PRIMARY KEY,
    token VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




--
-- Create index `name` on table `games`
--
SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                     WHERE table_name = 'games' 
                     AND table_schema = @db_name
                     AND index_name = 'name');
                     
SET @sqlstmt = IF(@index_exists = 0, 
    'ALTER TABLE games ADD UNIQUE INDEX name (name)', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Проверка и добавление created_at в таблицу games
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'games' 
                   AND table_schema = @db_name
                   AND column_name = 'created_at');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE games ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Проверка и добавление updated_at в таблицу games
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'games' 
                   AND table_schema = @db_name
                   AND column_name = 'updated_at');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE games ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'games' 
                   AND table_schema = @db_name
                   AND column_name = 'image');
                   
SET @sqlstmt = IF(@col_exists = 0,
    'ALTER TABLE games ADD COLUMN image VARCHAR(255) NOT NULL DEFAULT "" AFTER name',
    'SELECT 1');
    
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Проверка наличия записей в таблице games
SET @records_exist = (SELECT COUNT(*) FROM games);

-- Вставка данных только если таблица пуста
SET @sqlstmt = IF(@records_exist = 0, 
    'INSERT INTO games (id, image, name, team_size, created_at, updated_at) VALUES
    (1, "games/cs2.jpg", "CS2", 5, NOW(), NOW()),
    (2, "games/dota2.jpg", "Dota 2", 5, NOW(), NOW()),
    (3, "games/pubg.jpg", "PUBG", 4, NOW(), NOW())', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Проверка существования таблицы game_matches
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'game_matches' 
                     AND table_schema = @db_name);

-- Проверка существования временной таблицы
SET @temp_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name = 'game_matches_temp' 
                    AND table_schema = @db_name);

-- Создание временной таблицы и пересоздание game_matches
DROP PROCEDURE IF EXISTS recreate_game_matches;
DELIMITER //
CREATE PROCEDURE recreate_game_matches()
BEGIN
    -- Удаляем временную таблицу, если она существует
    IF @temp_exists > 0 THEN
        DROP TABLE game_matches_temp;
    END IF;
    
    IF @table_exists > 0 THEN
        -- Создаем временную таблицу
        CREATE TABLE game_matches_temp AS SELECT * FROM game_matches;
        
        -- Удаляем оригинальную таблицу
        DROP TABLE game_matches;
    END IF;
    
    -- Создание таблицы game_matches с правильной структурой, но без внешнего ключа
    CREATE TABLE IF NOT EXISTS game_matches (
      id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
      game_type varchar(255) NOT NULL,
      session_id varchar(255) NOT NULL,
      match_id varchar(255) DEFAULT NULL,
      score int(11) DEFAULT NULL,
      match_score varchar(45) DEFAULT NULL,
      date_scan datetime DEFAULT NULL,
      col_scan int(11) NOT NULL DEFAULT 0,
      player_id int(11) UNSIGNED NOT NULL,
      created_at timestamp NULL DEFAULT NULL,
      updated_at timestamp NULL DEFAULT NULL,
      PRIMARY KEY (id),
      KEY game_matches_user_id_index (player_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
END //
DELIMITER ;

CALL recreate_game_matches();
DROP PROCEDURE IF EXISTS recreate_game_matches;

-- Восстановление данных, если была создана временная таблица
SET @temp_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name = 'game_matches_temp' 
                    AND table_schema = @db_name);
SET @sqlstmt = IF(@temp_exists > 0, 
    'INSERT INTO game_matches (id, game_type, session_id, match_id, score, match_score, date_scan, col_scan, player_id, created_at, updated_at)
     SELECT id, game_type, session_id, match_id, score, match_score, date_scan, col_scan, player_id, created_at, updated_at
     FROM game_matches_temp;', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Удаление временной таблицы, если она существует
SET @sqlstmt = IF(@temp_exists > 0, 
    'DROP TABLE game_matches_temp', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Проверяем существование таблицы rating
SET @rating_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'rating' 
                     AND table_schema = @db_name);

-- Проверяем существование столбцов в таблице rating
SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = 'rating' 
                        AND table_schema = @db_name
                        AND column_name = 'player_id');

SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                      WHERE table_name = 'rating' 
                      AND table_schema = @db_name
                      AND column_name = 'user_id');

SET @game_id_type = (SELECT DATA_TYPE FROM information_schema.columns 
                    WHERE table_name = 'rating' 
                    AND table_schema = @db_name
                    AND column_name = 'game_id');

-- Проверяем наличие триггеров для таблицы rating
SET @rating_triggers = (SELECT COUNT(*) FROM information_schema.TRIGGERS 
                       WHERE EVENT_OBJECT_TABLE = 'rating' 
                       AND EVENT_OBJECT_SCHEMA = @db_name);

-- Если есть триггеры, удаляем их по одному
DROP PROCEDURE IF EXISTS drop_rating_triggers;
DELIMITER //
CREATE PROCEDURE drop_rating_triggers()
BEGIN
    -- Удаляем известные триггеры для таблицы rating
    DROP TRIGGER IF EXISTS rating_insert_sync;
    DROP TRIGGER IF EXISTS rating_update_sync;
    DROP TRIGGER IF EXISTS rating_before_insert;
    DROP TRIGGER IF EXISTS rating_before_update;
    DROP TRIGGER IF EXISTS rating_after_insert;
    DROP TRIGGER IF EXISTS rating_after_update;
    
    SELECT 'Удалены триггеры для таблицы rating' AS message;
END //
DELIMITER ;

-- Вызываем процедуру для удаления триггеров
CALL drop_rating_triggers();
DROP PROCEDURE IF EXISTS drop_rating_triggers;

-- Создаем процедуру для обновления таблицы rating
DROP PROCEDURE IF EXISTS update_rating_table;
DELIMITER //
CREATE PROCEDURE update_rating_table()
BEGIN
    -- Проверяем существование таблицы rating
    SET @rating_exists = (SELECT COUNT(*) FROM information_schema.tables 
                         WHERE table_name = 'rating' 
                         AND table_schema = @db_name);
    
    -- Если таблица rating существует
    IF @rating_exists > 0 THEN
        -- Проверяем тип данных поля game_id
        SET @game_id_type = (SELECT DATA_TYPE FROM information_schema.COLUMNS 
                            WHERE TABLE_SCHEMA = @db_name 
                            AND TABLE_NAME = 'rating' 
                            AND COLUMN_NAME = 'game_id');
        
        -- Проверяем наличие столбца user_id
        SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                              WHERE table_name = 'rating' 
                              AND table_schema = @db_name
                              AND column_name = 'user_id');
        
        -- Проверяем наличие столбца player_id
        SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                WHERE table_name = 'rating' 
                                AND table_schema = @db_name
                                AND column_name = 'player_id');
        
        -- Проверяем наличие столбцов created_at и updated_at
        SET @created_at_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                 WHERE table_name = 'rating' 
                                 AND table_schema = @db_name
                                 AND column_name = 'created_at');
                                 
        SET @updated_at_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                 WHERE table_name = 'rating' 
                                 AND table_schema = @db_name
                                 AND column_name = 'updated_at');
        
        -- 1. Удаляем поле user_id, если оно существует
        IF @user_id_exists > 0 THEN
            -- Теперь можно безопасно удалить столбец
            ALTER TABLE rating DROP COLUMN user_id;
            SELECT 'Удален столбец user_id из таблицы rating' AS message;
        END IF;
        
        -- 2. Проверяем тип данных поля game_id и преобразуем его при необходимости
        IF @game_id_type = 'varchar' THEN
            -- Создаем временную таблицу для хранения данных
            CREATE TEMPORARY TABLE temp_rating (
                id INT NOT NULL,
                club_id INT,
                player_id INT,
                new_game_id INT,
                game_rating INT,
                season_id INT,
                created_at TIMESTAMP NULL,
                updated_at TIMESTAMP NULL
            );
            
            -- Вставляем данные с преобразованием game_id
            INSERT INTO temp_rating (id, club_id, player_id, new_game_id, game_rating, season_id, created_at, updated_at)
            SELECT 
                id,
                club_id,
                player_id,
                CASE 
                    WHEN game_id = 'cs2' THEN 1
                    WHEN game_id = 'dota2' THEN 2
                    WHEN game_id = 'pubg' THEN 3
                    WHEN game_id = '1' THEN 1
                    WHEN game_id = '2' THEN 2
                    WHEN game_id = '3' THEN 3
                    ELSE NULL
                END,
                game_rating,
                season_id,
                CASE WHEN @created_at_exists > 0 THEN created_at ELSE NULL END,
                CASE WHEN @updated_at_exists > 0 THEN updated_at ELSE NULL END
            FROM rating
            WHERE game_id IN ('cs2', 'dota2', 'pubg', '1', '2', '3');
            
            -- Удаляем все данные из основной таблицы
            TRUNCATE TABLE rating;
            
            -- Изменяем тип данных поля game_id на bigint
            ALTER TABLE rating MODIFY COLUMN game_id bigint(20) UNSIGNED;
            
            -- Вставляем данные обратно с преобразованными значениями game_id
            INSERT INTO rating (id, club_id, player_id, game_id, game_rating, season_id, 
                               created_at, updated_at)
            SELECT id, club_id, player_id, new_game_id, game_rating, season_id, 
                   created_at, updated_at
            FROM temp_rating 
            WHERE new_game_id IS NOT NULL;
            
            -- Удаляем временную таблицу
            DROP TEMPORARY TABLE IF EXISTS temp_rating;
            
            SELECT 'Обновлен тип данных поля game_id и преобразованы значения' AS message;
        ELSE
            -- Если поле уже имеет числовой тип, просто обновляем значения
            -- Используем безопасное преобразование с CASE
            UPDATE rating SET game_id = 1 WHERE game_id = 1 OR CAST(game_id AS CHAR) = 'cs2';
            UPDATE rating SET game_id = 2 WHERE game_id = 2 OR CAST(game_id AS CHAR) = 'dota2';
            UPDATE rating SET game_id = 3 WHERE game_id = 3 OR CAST(game_id AS CHAR) = 'pubg';
            
            SELECT 'Преобразованы значения поля game_id' AS message;
        END IF;
        
        -- Добавляем столбцы created_at и updated_at, если их нет
        IF @created_at_exists = 0 THEN
            ALTER TABLE rating ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL;
            SELECT 'Добавлен столбец created_at в таблицу rating' AS message;
        END IF;
        
        IF @updated_at_exists = 0 THEN
            ALTER TABLE rating ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL;
            SELECT 'Добавлен столбец updated_at в таблицу rating' AS message;
        END IF;
        
        -- 3. Проверяем наличие индекса для game_id
        SET @game_id_index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                                    WHERE table_name = 'rating' 
                                    AND table_schema = @db_name
                                    AND index_name = 'rating_game_id_index');
        
        -- Добавляем индекс для game_id, если его нет
        IF @game_id_index_exists = 0 THEN
            ALTER TABLE rating ADD INDEX rating_game_id_index (game_id);
            SELECT 'Добавлен индекс для столбца game_id в таблице rating' AS message;
        END IF;
        
        -- 4. Проверяем наличие поля player_id
        IF @player_id_exists = 0 THEN
            ALTER TABLE rating ADD COLUMN player_id int(11) UNSIGNED NOT NULL AFTER club_id;
            SELECT 'Добавлен столбец player_id в таблицу rating' AS message;
        END IF;
        
        -- 5. Проверяем наличие индекса для player_id
        SET @player_id_index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                                      WHERE table_name = 'rating' 
                                      AND table_schema = @db_name
                                      AND index_name = 'rating_player_id_index');
        
        -- Добавляем индекс для player_id, если его нет
        IF @player_id_index_exists = 0 THEN
            ALTER TABLE rating ADD INDEX rating_player_id_index (player_id);
            SELECT 'Добавлен индекс для столбца player_id в таблице rating' AS message;
        END IF;
        
        -- 6. Проверяем наличие поля game_now и удаляем его, если оно есть
        SET @game_now_exists = (SELECT COUNT(*) FROM information_schema.columns 
                               WHERE table_name = 'rating' 
                               AND table_schema = @db_name
                               AND column_name = 'game_now');
        
        IF @game_now_exists > 0 THEN
            ALTER TABLE rating DROP COLUMN game_now;
            SELECT 'Удален столбец game_now из таблицы rating' AS message;
        END IF;
        
        -- 7. Проверяем наличие уникального индекса с user_id и удаляем его
        SET @old_unique_index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                                      WHERE table_name = 'rating' 
                                      AND table_schema = @db_name
                                      AND index_name = 'rating_user_game_club_season_unique');
        
        IF @old_unique_index_exists > 0 THEN
            ALTER TABLE rating DROP INDEX rating_user_game_club_season_unique;
            SELECT 'Удален старый уникальный индекс rating_user_game_club_season_unique' AS message;
        END IF;
        
        -- 8. Проверяем наличие нового уникального индекса
        SET @unique_index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                                   WHERE table_name = 'rating' 
                                   AND table_schema = @db_name
                                   AND index_name = 'rating_player_game_club_season_unique');
        
        -- Добавляем уникальный индекс, если его нет
        IF @unique_index_exists = 0 THEN
            ALTER TABLE rating ADD UNIQUE INDEX rating_player_game_club_season_unique 
                (player_id, game_id, club_id, season_id);
            SELECT 'Добавлен уникальный индекс для столбцов player_id, game_id, club_id, season_id в таблице rating' AS message;
        END IF;
    ELSE
        SELECT 'Таблица rating не существует' AS message;
    END IF;
END //
DELIMITER ;

-- Вызываем процедуру
CALL update_rating_table();
DROP PROCEDURE IF EXISTS update_rating_table;

-- Если таблица уже существует и использует player_id, добавляем скрипт для миграции данных
-- Проверяем существование столбца player_id в таблице rating
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'rating' 
                   AND table_schema = @db_name
                   AND column_name = 'player_id');

-- Если player_id существует, но user_id нет, добавляем user_id и копируем данные 
-- ??? Зачем player_id переименовываем в  user_id???
/*DROP PROCEDURE IF EXISTS migrate_player_id_to_user_id;
DELIMITER //
CREATE PROCEDURE migrate_player_id_to_user_id()
BEGIN
    IF @col_exists > 0 THEN
        -- Проверяем существование столбца user_id
        SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                              WHERE table_name = 'rating' 
                              AND table_schema = @db_name
                              AND column_name = 'user_id');
        
        -- Если user_id не существует, добавляем его
        IF @user_id_exists = 0 THEN
            ALTER TABLE rating ADD COLUMN user_id int(11) UNSIGNED NOT NULL AFTER club_id;
        END IF;
        
        -- Копируем данные из player_id в user_id
        UPDATE rating SET user_id = player_id;
        
        -- Удаляем столбец player_id
        ALTER TABLE rating DROP COLUMN player_id;
    END IF;
END //
DELIMITER ;

CALL migrate_player_id_to_user_id();
DROP PROCEDURE IF EXISTS migrate_player_id_to_user_id;*/

-- Проверяем и обновляем другие таблицы, которые могут использовать player_id
-- Например, для таблицы match_results (если она существует)
/*SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'match_results' 
                   AND table_schema =  @db_name
                   AND column_name = 'player_id');

DROP PROCEDURE IF EXISTS update_match_results_columns;
DELIMITER //
CREATE PROCEDURE update_match_results_columns()
BEGIN
    IF @col_exists > 0 THEN
        -- Проверяем, существует ли уже столбец user_id
        SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                              WHERE table_name = 'match_results' 
                              AND table_schema = @db_name
                              AND column_name = 'user_id');
        
        -- Добавляем столбец user_id только если он не существует
        IF @user_id_exists = 0 THEN
            ALTER TABLE match_results ADD COLUMN user_id int(11) UNSIGNED NOT NULL AFTER match_id;
        END IF;
        
        -- Обновляем данные
        UPDATE match_results SET user_id = player_id WHERE player_id IS NOT NULL;
        
        -- Удаляем столбец player_id
        ALTER TABLE match_results DROP COLUMN player_id;
    END IF;
END //
DELIMITER ;

CALL update_match_results_columns();
DROP PROCEDURE IF EXISTS update_match_results_columns;*/
/*
-- Проверяем существование столбца user_id в таблице match_results
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'match_results' 
                     AND table_schema = @db_name);

SET @user_id_exists = IF(@table_exists > 0, 
                       (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = 'match_results' 
                        AND table_schema = @db_name
                        AND column_name = 'user_id'), 
                       0);

SET @player_id_exists = IF(@table_exists > 0, 
                         (SELECT COUNT(*) FROM information_schema.columns 
                          WHERE table_name = 'match_results' 
                          AND table_schema = @db_name
                          AND column_name = 'player_id'), 
                         0);

-- Если таблица существует и нет столбца user_id, добавляем его
DROP PROCEDURE IF EXISTS add_user_id_to_match_results;
DELIMITER //
CREATE PROCEDURE add_user_id_to_match_results()
BEGIN
    IF @table_exists > 0 AND @user_id_exists = 0 THEN
        -- Добавляем столбец user_id
        ALTER TABLE match_results ADD COLUMN user_id int(11) UNSIGNED DEFAULT NULL AFTER match_id;
        
        -- Если есть столбец player_id, копируем данные
        IF @player_id_exists > 0 THEN
            UPDATE match_results SET user_id = player_id WHERE player_id IS NOT NULL;
        END IF;
    END IF;
END //
DELIMITER ;

CALL add_user_id_to_match_results();
DROP PROCEDURE IF EXISTS add_user_id_to_match_results;

-- Если таблица существует и нет столбца player_id, но есть user_id, добавляем player_id
DROP PROCEDURE IF EXISTS add_player_id_to_match_results;
DELIMITER //
CREATE PROCEDURE add_player_id_to_match_results()
BEGIN
    IF @table_exists > 0 AND @player_id_exists = 0 AND @user_id_exists > 0 THEN
        -- Добавляем столбец player_id
        ALTER TABLE match_results ADD COLUMN player_id int(11) UNSIGNED DEFAULT NULL AFTER user_id;
        
        -- Копируем данные из user_id в player_id
        UPDATE match_results SET player_id = user_id WHERE user_id IS NOT NULL;
    END IF;
END //
DELIMITER ;

CALL add_player_id_to_match_results();
DROP PROCEDURE IF EXISTS add_player_id_to_match_results;

-- Проверяем, нужно ли создавать триггеры
SET @need_triggers = IF(@table_exists > 0 AND @user_id_exists > 0 AND @player_id_exists > 0, 1, 0);

-- Создаем триггер для синхронизации player_id и user_id при вставке
SET @sqlstmt = IF(@need_triggers = 1, 
    'DROP TRIGGER IF EXISTS match_results_insert_sync', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sqlstmt = IF(@need_triggers = 1, 
    'CREATE TRIGGER match_results_insert_sync BEFORE INSERT ON match_results
     FOR EACH ROW
     BEGIN
         IF NEW.user_id IS NULL AND NEW.player_id IS NOT NULL THEN
             SET NEW.user_id = NEW.player_id;
         ELSEIF NEW.player_id IS NULL AND NEW.user_id IS NOT NULL THEN
             SET NEW.player_id = NEW.user_id;
         END IF;
     END', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Создаем триггер для синхронизации player_id и user_id при обновлении
SET @sqlstmt = IF(@need_triggers = 1, 
    'DROP TRIGGER IF EXISTS match_results_update_sync', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sqlstmt = IF(@need_triggers = 1, 
    'CREATE TRIGGER match_results_update_sync BEFORE UPDATE ON match_results
     FOR EACH ROW
     BEGIN
         IF NEW.user_id <> OLD.user_id AND NEW.player_id = OLD.player_id THEN
             SET NEW.player_id = NEW.user_id;
         ELSEIF NEW.player_id <> OLD.player_id AND NEW.user_id = OLD.user_id THEN
             SET NEW.user_id = NEW.player_id;
         END IF;
     END', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
*/
-- Проверяем структуру таблицы game_matches;

-- Проверяем структуру таблицы players;

-- Проверяем существование столбца user_id в таблице game_matches
/*
SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                      WHERE table_name = 'game_matches' 
                      AND table_schema = @db_name
                      AND column_name = 'user_id');

-- Проверяем существование столбца player_id в таблице game_matches
SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = 'game_matches' 
                        AND table_schema = @db_name
                        AND column_name = 'player_id');

-- Если есть user_id, но нет player_id, добавляем player_id и копируем данные
DROP PROCEDURE IF EXISTS add_player_id_to_game_matches;
DELIMITER //
CREATE PROCEDURE add_player_id_to_game_matches()
BEGIN
    IF @user_id_exists > 0 AND @player_id_exists = 0 THEN
        -- Добавляем столбец player_id
        ALTER TABLE game_matches 
        ADD COLUMN player_id int(11) UNSIGNED DEFAULT NULL AFTER user_id;
        
        -- Копируем данные из user_id в player_id
        UPDATE game_matches SET player_id = user_id WHERE user_id IS NOT NULL;
        
        -- Добавляем индекс для player_id вместо внешнего ключа
        ALTER TABLE game_matches ADD INDEX game_matches_player_id_index (player_id);
    END IF;
END //
DELIMITER ;

CALL add_player_id_to_game_matches();
DROP PROCEDURE IF EXISTS add_player_id_to_game_matches;

-- Альтернативный подход - создание индексов вместо внешних ключей
DROP PROCEDURE IF EXISTS add_team_members_indexes;
DELIMITER //
CREATE PROCEDURE add_team_members_indexes()
BEGIN
    -- Проверяем наличие индекса для team_id
    SET @index_team_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                             WHERE table_name = 'team_members' 
                             AND table_schema = DATABASE()
                             AND index_name = 'team_members_team_id_index');
                             
    -- Проверяем наличие индекса для player_id
    SET @index_player_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                               WHERE table_name = 'team_members' 
                               AND table_schema = DATABASE()
                               AND index_name = 'team_members_player_id_index');
    
    -- Добавляем индекс для team_id, если он не существует
    IF @index_team_exists = 0 THEN
        ALTER TABLE team_members ADD INDEX team_members_team_id_index (team_id);
        SELECT 'Индекс для team_id добавлен в таблицу team_members' AS message;
    END IF;
    
    -- Добавляем индекс для player_id, если он не существует
    IF @index_player_exists = 0 THEN
        ALTER TABLE team_members ADD INDEX team_members_player_id_index (player_id);
        SELECT 'Индекс для player_id добавлен в таблицу team_members' AS message;
    END IF;
    
    -- Добавляем комментарий о том, что внешние ключи не были созданы
    SELECT 'Внешние ключи не были созданы из-за возможных проблем с совместимостью данных. Вместо этого были добавлены индексы.' AS message;
END //
DELIMITER ;

CALL add_team_members_indexes();
DROP PROCEDURE IF EXISTS add_team_members_indexes;

-- Проверка и добавление столбца season_id в таблицу rating
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'rating' 
                   AND table_schema = @db_name
                   AND column_name = 'season_id');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE rating ADD COLUMN season_id bigint(20) UNSIGNED DEFAULT NULL AFTER game_rating', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Добавление внешнего ключа для season_id
SET @fk_exists = (SELECT COUNT(*) FROM information_schema.table_constraints 
                  WHERE table_name = 'rating' 
                  AND table_schema = @db_name
                  AND constraint_name = 'rating_season_id_foreign');

-- Пропускаем создание внешнего ключа, так как оно вызывает ошибку
-- Вместо этого добавляем индекс для season_id
SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                    WHERE table_name = 'rating' 
                    AND table_schema = @db_name
                    AND index_name = 'rating_season_id_index');
SET @sqlstmt = IF(@fk_exists = 0 AND @index_exists = 0, 
    'ALTER TABLE rating ADD INDEX rating_season_id_index (season_id)', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;

-- Обновление уникального индекса для включения season_id
SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                     WHERE table_name = 'rating' 
                     AND table_schema = @db_name
                     AND index_name = 'rating_user_game_club_season_unique');
SET @old_index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                         WHERE table_name = 'rating' 
                         AND table_schema = @db_name
                         AND index_name = 'rating_user_game_club_unique');

-- Если старый индекс существует, но нового нет, создаем новый и удаляем старый
DROP PROCEDURE IF EXISTS update_rating_unique_index;
DELIMITER //
CREATE PROCEDURE update_rating_unique_index()
BEGIN
    IF @old_index_exists > 0 AND @index_exists = 0 THEN
        -- Проверяем, какое имя столбца используется: user_id или player_id
        SET @user_col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                               WHERE table_name = 'rating' 
                               AND table_schema = @db_name
                               AND column_name = 'user_id');
        
        SET @player_col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                 WHERE table_name = 'rating' 
                                 AND table_schema = @db_name
                                 AND column_name = 'player_id');
        
        -- Используем соответствующее имя столбца
        IF @user_col_exists > 0 THEN
            ALTER TABLE rating 
            DROP INDEX rating_user_game_club_unique, 
            ADD UNIQUE INDEX rating_user_game_club_season_unique (user_id, game_id, club_id, season_id);
        ELSEIF @player_col_exists > 0 THEN
            ALTER TABLE rating 
            DROP INDEX rating_user_game_club_unique, 
            ADD UNIQUE INDEX rating_user_game_club_season_unique (player_id, game_id, club_id, season_id);
        END IF;
    END IF;
END //
DELIMITER ;

CALL update_rating_unique_index();
DROP PROCEDURE IF EXISTS update_rating_unique_index;

-- Если ни старого, ни нового индекса нет, просто создаем новый
DROP PROCEDURE IF EXISTS create_rating_unique_index;
DELIMITER //
CREATE PROCEDURE create_rating_unique_index()
BEGIN
    IF @old_index_exists = 0 AND @index_exists = 0 THEN
        -- Проверяем, какое имя столбца используется: user_id или player_id
        SET @user_col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                               WHERE table_name = 'rating' 
                               AND table_schema = @db_name
                               AND column_name = 'user_id');
        
        SET @player_col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                 WHERE table_name = 'rating' 
                                 AND table_schema = @db_name
                                 AND column_name = 'player_id');
        
        -- Используем соответствующее имя столбца
        IF @user_col_exists > 0 THEN
            ALTER TABLE rating 
            ADD UNIQUE INDEX rating_user_game_club_season_unique (user_id, game_id, club_id, season_id);
        ELSEIF @player_col_exists > 0 THEN
            ALTER TABLE rating 
            ADD UNIQUE INDEX rating_user_game_club_season_unique (player_id, game_id, club_id, season_id);
        END IF;
    END IF;
END //
DELIMITER ;

CALL create_rating_unique_index();
DROP PROCEDURE IF EXISTS create_rating_unique_index;
*/
-- Проверка существования таблицы seasons
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'seasons' 
                     AND table_schema = @db_name);

-- Создание таблицы seasons, если она не существует
DROP PROCEDURE IF EXISTS create_seasons_table;
DELIMITER //
CREATE PROCEDURE create_seasons_table()
BEGIN
    IF @table_exists = 0 THEN
        CREATE TABLE seasons (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            start_date datetime NOT NULL,
            end_date datetime DEFAULT NULL,
            is_active tinyint(1) NOT NULL DEFAULT 1,
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    END IF;
END //
DELIMITER ;

CALL create_seasons_table();
DROP PROCEDURE IF EXISTS create_seasons_table;

/*-- Создание начального сезона, если таблица была только что создана
SET @sqlstmt = IF(@table_exists = 0, 
    'INSERT INTO seasons (name, start_date, end_date, is_active, created_at, updated_at)
     VALUES (CONCAT("Сезон ", YEAR(NOW())), NOW(), NULL, 1, NOW(), NOW())', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
*/
-- Проверка структуры таблицы team_members
DROP PROCEDURE IF EXISTS fix_team_members_table_structure;
DELIMITER //
CREATE PROCEDURE fix_team_members_table_structure()
BEGIN
    -- Проверяем существование таблицы
    SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                         WHERE table_name = 'team_members' 
                         AND table_schema = DATABASE());
                         
    -- Проверяем существование временной таблицы
    SET @temp_exists = (SELECT COUNT(*) FROM information_schema.tables 
                        WHERE table_name = 'team_members_temp' 
                        AND table_schema = DATABASE());
                        
    -- Если временная таблица существует, удаляем её
    IF @temp_exists > 0 THEN
        DROP TABLE team_members_temp;
    END IF;
    
    -- Если основная таблица существует, проверяем её структуру
    IF @table_exists > 0 THEN
        -- Проверяем наличие столбца id
        SET @has_id_column = (SELECT COUNT(*) FROM information_schema.columns 
                             WHERE table_name = 'team_members' 
                             AND table_schema = DATABASE()
                             AND column_name = 'id');
                             
        -- Проверяем наличие столбца user_id
        SET @has_user_id = (SELECT COUNT(*) FROM information_schema.columns 
                           WHERE table_name = 'team_members' 
                           AND table_schema = DATABASE()
                           AND column_name = 'user_id');
                           
        -- Проверяем наличие столбца player_id
        SET @has_player_id = (SELECT COUNT(*) FROM information_schema.columns 
                             WHERE table_name = 'team_members' 
                             AND table_schema = DATABASE()
                             AND column_name = 'player_id');
                             
        -- Проверяем наличие столбца created_at
        SET @has_created_at = (SELECT COUNT(*) FROM information_schema.columns 
                              WHERE table_name = 'team_members' 
                              AND table_schema = DATABASE()
                              AND column_name = 'created_at');
                              
        -- Проверяем наличие столбца updated_at
        SET @has_updated_at = (SELECT COUNT(*) FROM information_schema.columns 
                              WHERE table_name = 'team_members' 
                              AND table_schema = DATABASE()
                              AND column_name = 'updated_at');
        
        -- Если нет столбца id, создаем новую таблицу с правильной структурой
        IF @has_id_column = 0 THEN
            -- Создаем временную таблицу с правильной структурой
            CREATE TABLE team_members_temp (
                id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                team_id bigint(20) UNSIGNED NOT NULL,
                player_id bigint(20) UNSIGNED NOT NULL,
                role enum('captain','member') NOT NULL DEFAULT 'member',
                created_at timestamp NULL DEFAULT NULL,
                updated_at timestamp NULL DEFAULT NULL,
                PRIMARY KEY (id),
                UNIQUE KEY team_members_player_team_unique (player_id, team_id),
                KEY team_members_team_id_index (team_id),
                KEY team_members_player_id_index (player_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            
            -- Формируем динамический SQL для копирования данных
            SET @sql_columns = 'team_id';
            SET @sql_source_columns = 'team_id';
            
            -- Добавляем player_id или user_id в зависимости от того, что есть
            IF @has_player_id > 0 THEN
                SET @sql_columns = CONCAT(@sql_columns, ', player_id');
                SET @sql_source_columns = CONCAT(@sql_source_columns, ', player_id');
            ELSEIF @has_user_id > 0 THEN
                SET @sql_columns = CONCAT(@sql_columns, ', player_id');
                SET @sql_source_columns = CONCAT(@sql_source_columns, ', user_id');
            ELSE
                SET @sql_columns = CONCAT(@sql_columns, ', player_id');
                SET @sql_source_columns = CONCAT(@sql_source_columns, ', 0');
            END IF;
            
            -- Добавляем role
            SET @sql_columns = CONCAT(@sql_columns, ', role');
            SET @sql_source_columns = CONCAT(@sql_source_columns, ', role');
            
            -- Добавляем created_at и updated_at, если они есть
            IF @has_created_at > 0 THEN
                SET @sql_columns = CONCAT(@sql_columns, ', created_at');
                SET @sql_source_columns = CONCAT(@sql_source_columns, ', created_at');
            ELSE
                SET @sql_columns = CONCAT(@sql_columns, ', created_at');
                SET @sql_source_columns = CONCAT(@sql_source_columns, ', NOW()');
            END IF;
            
            IF @has_updated_at > 0 THEN
                SET @sql_columns = CONCAT(@sql_columns, ', updated_at');
                SET @sql_source_columns = CONCAT(@sql_source_columns, ', updated_at');
            ELSE
                SET @sql_columns = CONCAT(@sql_columns, ', updated_at');
                SET @sql_source_columns = CONCAT(@sql_source_columns, ', NOW()');
            END IF;
            
            -- Формируем и выполняем SQL запрос
            SET @sql = CONCAT('INSERT INTO team_members_temp (', @sql_columns, ') SELECT ', @sql_source_columns, ' FROM team_members');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- Удаляем старую таблицу
            DROP TABLE team_members;
            
            -- Переименовываем временную таблицу
            RENAME TABLE team_members_temp TO team_members;
            
            SELECT 'Таблица team_members была перестроена с добавлением столбца id' AS message;
        ELSE
            -- Если столбец id уже существует, проверяем наличие других необходимых столбцов
            
            -- Проверяем наличие столбца player_id
            IF @has_player_id = 0 THEN
                -- Если есть user_id, переименовываем его в player_id
                IF @has_user_id > 0 THEN
                    ALTER TABLE team_members CHANGE COLUMN user_id player_id bigint(20) UNSIGNED NOT NULL;
                    SELECT 'Столбец user_id был переименован в player_id в таблице team_members' AS message;
                ELSE
                    -- Если нет ни player_id, ни user_id, добавляем player_id
                    ALTER TABLE team_members ADD COLUMN player_id bigint(20) UNSIGNED NOT NULL AFTER team_id;
                    SELECT 'Столбец player_id был добавлен в таблицу team_members' AS message;
                END IF;
            END IF;
            
            -- Проверяем наличие столбца created_at
            IF @has_created_at = 0 THEN
                ALTER TABLE team_members ADD COLUMN created_at timestamp NULL DEFAULT NULL;
                SELECT 'Столбец created_at был добавлен в таблицу team_members' AS message;
            END IF;
            
            -- Проверяем наличие столбца updated_at
            IF @has_updated_at = 0 THEN
                ALTER TABLE team_members ADD COLUMN updated_at timestamp NULL DEFAULT NULL;
                SELECT 'Столбец updated_at был добавлен в таблицу team_members' AS message;
            END IF;
        END IF;
    ELSE
        -- Если таблица не существует, создаем её
        CREATE TABLE team_members (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            team_id bigint(20) UNSIGNED NOT NULL,
            player_id bigint(20) UNSIGNED NOT NULL,
            role enum('captain','member') NOT NULL DEFAULT 'member',
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY team_members_player_team_unique (player_id, team_id),
            KEY team_members_team_id_index (team_id),
            KEY team_members_player_id_index (player_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        
        SELECT 'Таблица team_members была создана' AS message;
    END IF;
END //
DELIMITER ;

CALL fix_team_members_table_structure();
DROP PROCEDURE IF EXISTS fix_team_members_table_structure;

-- Добавление внешних ключей для таблицы team_members
DROP PROCEDURE IF EXISTS add_team_members_foreign_keys;
DELIMITER //
CREATE PROCEDURE add_team_members_foreign_keys()
BEGIN
    DECLARE CONTINUE HANDLER FOR 1452, 1005, 1022, 1025 BEGIN
        -- Обработка ошибок: 
        -- 1452: Cannot add or update a child row: a foreign key constraint fails
        -- 1005: Can't create table (errno: 150)
        -- 1022: Can't write; duplicate key in table
        -- 1025: Error on rename
        SELECT CONCAT('Предупреждение: Не удалось создать внешний ключ для таблицы team_members. ', 
                     'Проверьте соответствие типов данных и наличие индексов.') AS message;
    END;

    -- Проверяем существование внешнего ключа для team_id
    SET @fk_team_exists = (SELECT COUNT(*) FROM information_schema.table_constraints 
                          WHERE table_name = 'team_members' 
                          AND table_schema = DATABASE()
                          AND constraint_name = 'team_members_team_id_foreign');
                          
    -- Проверяем существование внешнего ключа для player_id
    SET @fk_player_exists = (SELECT COUNT(*) FROM information_schema.table_constraints 
                            WHERE table_name = 'team_members' 
                            AND table_schema = DATABASE()
                            AND constraint_name = 'team_members_player_id_foreign');
    
    -- Проверяем типы данных в таблицах
    SET @team_id_type = (SELECT COLUMN_TYPE FROM information_schema.COLUMNS 
                        WHERE TABLE_SCHEMA = DATABASE() 
                        AND TABLE_NAME = 'teams' 
                        AND COLUMN_NAME = 'id');
                        
    SET @player_id_type = (SELECT COLUMN_TYPE FROM information_schema.COLUMNS 
                          WHERE TABLE_SCHEMA = DATABASE() 
                          AND TABLE_NAME = 'players' 
                          AND COLUMN_NAME = 'id');
                          
    SET @tm_team_id_type = (SELECT COLUMN_TYPE FROM information_schema.COLUMNS 
                           WHERE TABLE_SCHEMA = DATABASE() 
                           AND TABLE_NAME = 'team_members' 
                           AND COLUMN_NAME = 'team_id');
                           
    SET @tm_player_id_type = (SELECT COLUMN_TYPE FROM information_schema.COLUMNS 
                             WHERE TABLE_SCHEMA = DATABASE() 
                             AND TABLE_NAME = 'team_members' 
                             AND COLUMN_NAME = 'player_id');
    
    -- Проверяем и исправляем типы данных, если они не совпадают
    IF @tm_team_id_type != @team_id_type AND @team_id_type IS NOT NULL THEN
        SET @sql = CONCAT('ALTER TABLE team_members MODIFY COLUMN team_id ', @team_id_type, ' NOT NULL');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
    
    IF @tm_player_id_type != @player_id_type AND @player_id_type IS NOT NULL THEN
        SET @sql = CONCAT('ALTER TABLE team_members MODIFY COLUMN player_id ', @player_id_type, ' NOT NULL');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
    
    -- Добавляем внешний ключ для team_id, если он не существует
    IF @fk_team_exists = 0 THEN
        -- Сначала добавляем индекс, если его нет
        SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                            WHERE table_name = 'team_members' 
                            AND table_schema = DATABASE()
                            AND index_name = 'team_members_team_id_index');
                            
        IF @index_exists = 0 THEN
            ALTER TABLE team_members ADD INDEX team_members_team_id_index (team_id);
        END IF;
        
        -- Теперь пробуем добавить внешний ключ
        ALTER TABLE team_members
        ADD CONSTRAINT team_members_team_id_foreign
        FOREIGN KEY (team_id) REFERENCES teams (id) ON DELETE CASCADE;
    END IF;
    
    -- Добавляем внешний ключ для player_id, если он не существует
    IF @fk_player_exists = 0 THEN
        -- Сначала добавляем индекс, если его нет
        SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                            WHERE table_name = 'team_members' 
                            AND table_schema = DATABASE()
                            AND index_name = 'team_members_player_id_index');
                            
        IF @index_exists = 0 THEN
            ALTER TABLE team_members ADD INDEX team_members_player_id_index (player_id);
        END IF;
        
        -- Теперь пробуем добавить внешний ключ
        ALTER TABLE team_members
        ADD CONSTRAINT team_members_player_id_foreign
        FOREIGN KEY (player_id) REFERENCES players (id) ON DELETE CASCADE;
    END IF;
END //
DELIMITER ;

CALL add_team_members_foreign_keys();
DROP PROCEDURE IF EXISTS add_team_members_foreign_keys;

-- Проверка существования таблицы team_requests
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'team_requests' 
                     AND table_schema = @db_name);

-- Проверка существования таблицы teams
SET @teams_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name = 'teams' 
                    AND table_schema = @db_name);

-- Проверка существования таблицы players
SET @players_exists = (SELECT COUNT(*) FROM information_schema.tables 
                      WHERE table_name = 'players' 
                      AND table_schema = @db_name);

-- Создание таблицы team_requests, если она не существует
DROP PROCEDURE IF EXISTS create_team_requests_table;
DELIMITER //
CREATE PROCEDURE create_team_requests_table()
BEGIN
    DECLARE CONTINUE HANDLER FOR 1452, 1005, 1022, 1025 BEGIN
        -- Обработка ошибок: 
        -- 1452: Cannot add or update a child row: a foreign key constraint fails
        -- 1005: Can't create table (errno: 150)
        -- 1022: Can't write; duplicate key in table
        -- 1025: Error on rename
        SELECT CONCAT('Предупреждение: Не удалось создать внешний ключ для таблицы team_requests. ', 
                     'Создаем таблицу без внешних ключей.') AS message;
    END;

    -- Получаем типы данных из родительских таблиц
    SET @team_id_type = (SELECT COLUMN_TYPE FROM information_schema.COLUMNS 
                        WHERE TABLE_SCHEMA = @db_name 
                        AND TABLE_NAME = 'teams' 
                        AND COLUMN_NAME = 'id');
                        
    SET @player_id_type = (SELECT COLUMN_TYPE FROM information_schema.COLUMNS 
                          WHERE TABLE_SCHEMA = @db_name 
                          AND TABLE_NAME = 'players' 
                          AND COLUMN_NAME = 'id');
    
    -- Если типы не определены, используем стандартные
    IF @team_id_type IS NULL THEN
        SET @team_id_type = 'bigint(20) UNSIGNED';
    END IF;
    
    IF @player_id_type IS NULL THEN
        SET @player_id_type = 'bigint(20) UNSIGNED';
    END IF;

    IF @table_exists = 0 AND @teams_exists > 0 AND @players_exists > 0 THEN
        -- Сначала создаем таблицу без внешних ключей
        SET @sql = CONCAT('CREATE TABLE team_requests (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            team_id ', @team_id_type, ' NOT NULL,
            player_id ', @player_id_type, ' NOT NULL,
            status enum("pending","accepted","rejected") NOT NULL DEFAULT "pending",
            message text DEFAULT NULL,
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY team_requests_team_player_unique (team_id, player_id),
            KEY team_requests_team_id_index (team_id),
            KEY team_requests_player_id_index (player_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
        
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        SELECT 'Таблица team_requests создана без внешних ключей' AS message;
        
        -- Теперь пробуем добавить внешние ключи по отдельности
        BEGIN
            DECLARE CONTINUE HANDLER FOR 1452, 1005 BEGIN END;
            
            ALTER TABLE team_requests
            ADD CONSTRAINT team_requests_team_id_foreign
            FOREIGN KEY (team_id) REFERENCES teams (id) ON DELETE CASCADE;
            
            SELECT 'Внешний ключ team_id добавлен в таблицу team_requests' AS message;
        END;
        
        BEGIN
            DECLARE CONTINUE HANDLER FOR 1452, 1005 BEGIN END;
            
            ALTER TABLE team_requests
            ADD CONSTRAINT team_requests_player_id_foreign
            FOREIGN KEY (player_id) REFERENCES players (id) ON DELETE CASCADE;
            
            SELECT 'Внешний ключ player_id добавлен в таблицу team_requests' AS message;
        END;
        
    ELSEIF @table_exists > 0 THEN
        -- Проверяем, есть ли столбец user_id, но нет player_id
        SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                              WHERE table_name = 'team_requests' 
                              AND table_schema = @db_name
                              AND column_name = 'user_id');
                              
        SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                WHERE table_name = 'team_requests' 
                                AND table_schema = @db_name
                                AND column_name = 'player_id');
        
        -- Если есть user_id, но нет player_id, добавляем player_id и копируем данные
        IF @user_id_exists > 0 AND @player_id_exists = 0 THEN
            -- Получаем тип данных для user_id
            SET @user_id_type = (SELECT COLUMN_TYPE FROM information_schema.COLUMNS 
                                WHERE TABLE_SCHEMA = @db_name 
                                AND TABLE_NAME = 'team_requests' 
                                AND COLUMN_NAME = 'user_id');
            
            -- Добавляем столбец player_id с тем же типом, что и user_id
            SET @sql = CONCAT('ALTER TABLE team_requests ADD COLUMN player_id ', @user_id_type, ' NOT NULL AFTER user_id');
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- Копируем данные из user_id в player_id
            UPDATE team_requests SET player_id = user_id;
            
            -- Создаем индекс для player_id
            ALTER TABLE team_requests ADD INDEX team_requests_player_id_index (player_id);
            
            -- Пробуем добавить внешний ключ для player_id
            BEGIN
                DECLARE CONTINUE HANDLER FOR 1452, 1005 BEGIN END;
                
                ALTER TABLE team_requests 
                ADD CONSTRAINT team_requests_player_id_foreign 
                FOREIGN KEY (player_id) REFERENCES players (id) ON DELETE CASCADE;
            END;
            
            SELECT 'Столбец player_id добавлен в таблицу team_requests и данные скопированы из user_id' AS message;
        END IF;
    END IF;
END //
DELIMITER ;

CALL create_team_requests_table();
DROP PROCEDURE IF EXISTS create_team_requests_table;

-- Аналогично для таблицы team_invitations
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'team_invitations' 
                     AND table_schema = @db_name);

DROP PROCEDURE IF EXISTS create_team_invitations_table;
DELIMITER //
CREATE PROCEDURE create_team_invitations_table()
BEGIN
    IF @table_exists = 0 THEN
        CREATE TABLE team_invitations (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            team_id int(11) UNSIGNED NOT NULL,
            player_id int(11) UNSIGNED NOT NULL,
            invited_by int(11) UNSIGNED NOT NULL,
            status enum('pending','accepted','rejected') NOT NULL DEFAULT 'pending',
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY team_invitations_team_id_player_id_status_unique (team_id, player_id, status),
            KEY team_invitations_team_id_index (team_id),
            KEY team_invitations_player_id_index (player_id),
            KEY team_invitations_invited_by_index (invited_by)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        
        SELECT 'Таблица team_invitations создана с player_id вместо user_id' AS message;
    ELSEIF @table_exists > 0 THEN
        -- Проверяем, есть ли столбец user_id, но нет player_id
        SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                              WHERE table_name = 'team_invitations' 
                              AND table_schema = @db_name
                              AND column_name = 'user_id');
                              
        SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                WHERE table_name = 'team_invitations' 
                                AND table_schema = @db_name
                                AND column_name = 'player_id');
        
        -- Если есть user_id, но нет player_id, добавляем player_id и копируем данные
        IF @user_id_exists > 0 AND @player_id_exists = 0 THEN
            -- Добавляем столбец player_id
            ALTER TABLE team_invitations ADD COLUMN player_id int(11) UNSIGNED NOT NULL AFTER user_id;
            
            -- Копируем данные из user_id в player_id
            UPDATE team_invitations SET player_id = user_id;
            
            -- Создаем индекс для player_id
            ALTER TABLE team_invitations ADD INDEX team_invitations_player_id_index (player_id);
            
            SELECT 'Столбец player_id добавлен в таблицу team_invitations и данные скопированы из user_id' AS message;
        END IF;
    END IF;
END //
DELIMITER ;

CALL create_team_invitations_table();
DROP PROCEDURE IF EXISTS create_team_invitations_table;

/*-- Проверка и обновление таблицы rating
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'rating' 
                     AND table_schema = @db_name);

-- Проверка существования столбца player_id в таблице rating
SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = 'rating' 
                        AND table_schema = @db_name
                        AND column_name = 'player_id');

-- Проверка существования столбца user_id в таблице rating
SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                      WHERE table_name = 'rating' 
                      AND table_schema = @db_name
                      AND column_name = 'user_id');

-- Если таблица существует и есть player_id, но нет user_id, добавляем user_id
DROP PROCEDURE IF EXISTS update_rating_table;
DELIMITER //
CREATE PROCEDURE update_rating_table()
BEGIN
    IF @table_exists > 0 AND @player_id_exists > 0 AND @user_id_exists = 0 THEN
        -- Добавляем столбец user_id
        ALTER TABLE rating ADD COLUMN user_id int(11) UNSIGNED NOT NULL AFTER club_id;
        
        -- Копируем данные из player_id в user_id
        UPDATE rating SET user_id = player_id;
        
        -- Создаем индекс для user_id
        ALTER TABLE rating ADD INDEX rating_user_id_index (user_id);
    END IF;
END //
DELIMITER ;

CALL update_rating_table();
DROP PROCEDURE IF EXISTS update_rating_table;*/

/*-- Если таблица существует и есть user_id, но нет player_id, добавляем player_id
DROP PROCEDURE IF EXISTS add_player_id_to_rating;
DELIMITER //
CREATE PROCEDURE add_player_id_to_rating()
BEGIN
    IF @table_exists > 0 AND @user_id_exists > 0 AND @player_id_exists = 0 THEN
        -- Добавляем столбец player_id
        ALTER TABLE rating ADD COLUMN player_id int(11) UNSIGNED NOT NULL AFTER user_id;
        
        -- Копируем данные из user_id в player_id
        UPDATE rating SET player_id = user_id;
        
        -- Создаем индекс для player_id
        ALTER TABLE rating ADD INDEX rating_player_id_index (player_id);
    END IF;
END //
DELIMITER ;

CALL add_player_id_to_rating();
DROP PROCEDURE IF EXISTS add_player_id_to_rating;*/

/*-- Проверка и обновление таблицы team_members
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'team_members' 
                     AND table_schema = @db_name);

-- Проверка существования столбца player_id в таблице team_members
SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = 'team_members' 
                        AND table_schema = @db_name
                        AND column_name = 'player_id');

-- Проверка существования столбца user_id в таблице team_members
SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                      WHERE table_name = 'team_members' 
                      AND table_schema = @db_name
                      AND column_name = 'user_id');

-- Если таблица существует и есть user_id, но нет player_id, добавляем player_id
DROP PROCEDURE IF EXISTS add_player_id_to_team_members;
DELIMITER //
CREATE PROCEDURE add_player_id_to_team_members()
BEGIN
    IF @table_exists > 0 AND @user_id_exists > 0 AND @player_id_exists = 0 THEN
        -- Добавляем столбец player_id
        ALTER TABLE team_members ADD COLUMN player_id int(11) UNSIGNED NOT NULL AFTER user_id;
        
        -- Копируем данные из user_id в player_id
        UPDATE team_members SET player_id = user_id;
        
        -- Создаем индекс для player_id
        ALTER TABLE team_members ADD INDEX team_members_player_id_index (player_id);
    END IF;
END //
DELIMITER ;*/

/*CALL add_player_id_to_team_members();
DROP PROCEDURE IF EXISTS add_player_id_to_team_members;

-- Проверка и обновление таблицы game_matches
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'game_matches' 
                     AND table_schema = @db_name);

-- Проверка существования столбца player_id в таблице game_matches
SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = 'game_matches' 
                        AND table_schema = @db_name
                        AND column_name = 'player_id');

-- Проверка существования столбца user_id в таблице game_matches
SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                      WHERE table_name = 'game_matches' 
                      AND table_schema = @db_name
                      AND column_name = 'user_id');

-- Если таблица существует и есть user_id, но нет player_id, добавляем player_id
DROP PROCEDURE IF EXISTS add_player_id_to_game_matches;
DELIMITER //
CREATE PROCEDURE add_player_id_to_game_matches()
BEGIN
    IF @table_exists > 0 AND @user_id_exists > 0 AND @player_id_exists = 0 THEN
        -- Добавляем столбец player_id
        ALTER TABLE game_matches ADD COLUMN player_id int(11) UNSIGNED NOT NULL AFTER user_id;
        
        -- Копируем данные из user_id в player_id
        UPDATE game_matches SET player_id = user_id;
        
        -- Создаем индекс для player_id
        ALTER TABLE game_matches ADD INDEX game_matches_player_id_index (player_id);
    END IF;
END //
DELIMITER ;

CALL add_player_id_to_game_matches();
DROP PROCEDURE IF EXISTS add_player_id_to_game_matches;
SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = 'game_matches' 
                        AND table_schema = @db_name
                        AND column_name = 'player_id');

-- Проверка существования столбца user_id в таблице game_matches
SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                      WHERE table_name = 'game_matches' 
                      AND table_schema = @db_name
                      AND column_name = 'user_id');

-- Если таблица существует и есть user_id, но нет player_id, добавляем player_id
DROP PROCEDURE IF EXISTS add_player_id_to_game_matches;
DELIMITER //
CREATE PROCEDURE add_player_id_to_game_matches()
BEGIN
    IF @table_exists > 0 AND @user_id_exists > 0 AND @player_id_exists = 0 THEN
        -- Добавляем столбец player_id
        ALTER TABLE game_matches ADD COLUMN player_id int(11) UNSIGNED NOT NULL AFTER user_id;
        
        -- Копируем данные из user_id в player_id
        UPDATE game_matches SET player_id = user_id;
        
        -- Создаем индекс для player_id
        ALTER TABLE game_matches ADD INDEX game_matches_player_id_index (player_id);
    END IF;
END //
DELIMITER ;

CALL add_player_id_to_game_matches();
DROP PROCEDURE IF EXISTS add_player_id_to_game_matches;
SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                      WHERE table_name = 'game_matches' 
                      AND table_schema = @db_name
                      AND column_name = 'user_id');

-- Если таблица существует и есть user_id, но нет player_id, добавляем player_id
DROP PROCEDURE IF EXISTS add_player_id_to_game_matches;
DELIMITER //
CREATE PROCEDURE add_player_id_to_game_matches()
BEGIN
    IF @table_exists > 0 AND @user_id_exists > 0 AND @player_id_exists = 0 THEN
        -- Добавляем столбец player_id
        ALTER TABLE game_matches ADD COLUMN player_id int(11) UNSIGNED DEFAULT NULL AFTER user_id;
        
        -- Копируем данные из user_id в player_id
        UPDATE game_matches SET player_id = user_id WHERE user_id IS NOT NULL;
        
        -- Создаем индекс для player_id
        ALTER TABLE game_matches ADD INDEX game_matches_player_id_index (player_id);
    END IF;
END //
DELIMITER ;

CALL add_player_id_to_game_matches();
DROP PROCEDURE IF EXISTS add_player_id_to_game_matches;*/
/*замена
ALTER TABLE `rgtournament`.`game_matches` 
CHANGE COLUMN `user_id` `player_id` INT UNSIGNED NOT NULL ;
*/
-- Проверка существования таблицы match_results
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'match_results' 
                     AND table_schema = @db_name);

-- Если таблица не существует, создаем её
DROP PROCEDURE IF EXISTS create_match_results_table;
DELIMITER //
CREATE PROCEDURE create_match_results_table()
BEGIN
    IF @table_exists = 0 THEN
        CREATE TABLE match_results (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            match_id bigint(20) UNSIGNED NOT NULL,
            player_id int(11) UNSIGNED NOT NULL,
            victory tinyint(1) NOT NULL DEFAULT 0,
            round int(11) NOT NULL DEFAULT 1,
            start_math tinyint(1) NOT NULL DEFAULT 0,
            add_score int(11) NOT NULL DEFAULT 0,
            details json DEFAULT NULL,
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            KEY match_results_match_id_index (match_id),
            KEY match_results_player_id_index (player_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    END IF;
END //
DELIMITER ;

CALL create_match_results_table();
DROP PROCEDURE IF EXISTS create_match_results_table;

-- Проверка существования таблицы features
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'features' 
                     AND table_schema = @db_name);

-- Если таблица не существует, создаем её
DROP PROCEDURE IF EXISTS create_features_table;
DELIMITER //
CREATE PROCEDURE create_features_table()
BEGIN
    IF @table_exists = 0 THEN
        CREATE TABLE features (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            scope varchar(255) NOT NULL,
            value text NOT NULL,
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY features_name_scope_unique (name, scope)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    END IF;
END //
DELIMITER ;

CALL create_features_table();
DROP PROCEDURE IF EXISTS create_features_table;

-- Проверка существования таблицы team_invitations
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'team_invitations' 
                     AND table_schema = @db_name);

-- Если таблица не существует, создаем её
DROP PROCEDURE IF EXISTS create_team_invitations_table;
DELIMITER //
CREATE PROCEDURE create_team_invitations_table()
BEGIN
    IF @table_exists = 0 THEN
        CREATE TABLE team_invitations (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            team_id int(11) UNSIGNED NOT NULL,
            player_id int(11) UNSIGNED NOT NULL,
            invited_by int(11) UNSIGNED NOT NULL,
            status enum('pending','accepted','rejected') NOT NULL DEFAULT 'pending',
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY team_invitations_team_id_player_id_status_unique (team_id, player_id, status),
            KEY team_invitations_team_id_index (team_id),
            KEY team_invitations_player_id_index (player_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    END IF;
END //
DELIMITER ;

CALL create_team_invitations_table();
DROP PROCEDURE IF EXISTS create_team_invitations_table;

-- Проверка существования таблицы team_requests
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'team_requests' 
                     AND table_schema = @db_name);

-- Если таблица не существует, создаем её
DROP PROCEDURE IF EXISTS create_team_requests_table;
DELIMITER //
CREATE PROCEDURE create_team_requests_table()
BEGIN
    IF @table_exists = 0 THEN
        CREATE TABLE team_requests (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            team_id int(11) UNSIGNED NOT NULL,
            player_id int(11) UNSIGNED NOT NULL,
            status enum('pending','accepted','rejected') NOT NULL DEFAULT 'pending',
            message text DEFAULT NULL,
            created_at timestamp NULL DEFAULT NULL,
            updated_at timestamp NULL DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY team_requests_team_id_player_id_unique (team_id, player_id),
            KEY team_requests_team_id_index (team_id),
            KEY team_requests_player_id_index (player_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    END IF;
END //
DELIMITER ;

CALL create_team_requests_table();
DROP PROCEDURE IF EXISTS create_team_requests_table;

-- Проверка существования столбца description в таблице teams
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'teams' 
                   AND table_schema = @db_name
                   AND column_name = 'description');

-- Если столбец не существует, добавляем его
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE teams ADD COLUMN description VARCHAR(255) NULL AFTER name', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
/*
-- Проверка существования столбца password в таблице players
SET @password_exists = (SELECT COUNT(*) FROM information_schema.columns 
                       WHERE table_name = 'players' 
                       AND table_schema = @db_name
                       AND column_name = 'password');

-- Если столбец не существует, добавляем его
DROP PROCEDURE IF EXISTS add_password_to_players;
DELIMITER //
CREATE PROCEDURE add_password_to_players()
BEGIN
    IF @password_exists = 0 THEN
        -- Добавляем столбец password
        ALTER TABLE players ADD COLUMN password varchar(255) DEFAULT NULL AFTER email;
        
        -- Если есть столбец client_password, копируем данные
        SET @client_password_exists = (SELECT COUNT(*) FROM information_schema.columns 
                                      WHERE table_name = 'players' 
                                      AND table_schema = @db_name
                                      AND column_name = 'client_password');
        
        IF @client_password_exists > 0 THEN
            -- Обновляем пароли для всех пользователей
            UPDATE players SET password = client_password WHERE client_password IS NOT NULL;
        END IF;
    END IF;
END //
DELIMITER ;

CALL add_password_to_players();
DROP PROCEDURE IF EXISTS add_password_to_players;
*/
-- Проверяем наличие столбцов created_at и updated_at в таблице перед их использованием
DROP PROCEDURE IF EXISTS ensure_timestamp_columns;
DELIMITER //
CREATE PROCEDURE ensure_timestamp_columns(IN table_name VARCHAR(255))
BEGIN
    -- Проверяем существование столбца created_at
    SET @created_at_exists = (SELECT COUNT(*) FROM information_schema.columns 
                             WHERE table_name = table_name 
                             AND table_schema = @db_name
                             AND column_name = 'created_at');
    
    -- Проверяем существование столбца updated_at
    SET @updated_at_exists = (SELECT COUNT(*) FROM information_schema.columns 
                             WHERE table_name = table_name 
                             AND table_schema = @db_name
                             AND column_name = 'updated_at');
    
    -- Если столбца created_at нет, добавляем его
    IF @created_at_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', table_name, ' ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
    
    -- Если столбца updated_at нет, добавляем его
    IF @updated_at_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE ', table_name, ' ADD COLUMN updated_at TIMESTAMP NULL DEFAULT NULL');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //
DELIMITER ;

-- Вызываем процедуру для таблицы, в которой возникает ошибка
-- Замените 'your_table_name' на имя таблицы, в которой возникает ошибка
CALL ensure_timestamp_columns('game_matches');
CALL ensure_timestamp_columns('match_results');
CALL ensure_timestamp_columns('rating');
CALL ensure_timestamp_columns('team_invitations');
CALL ensure_timestamp_columns('team_requests');
CALL ensure_timestamp_columns('team_members');
CALL ensure_timestamp_columns('teams');
CALL ensure_timestamp_columns('games');
CALL ensure_timestamp_columns('players');
CALL ensure_timestamp_columns('clubs');

DROP PROCEDURE IF EXISTS ensure_timestamp_columns;

-- Проверка и добавление created_at в таблицу teams
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_name = 'teams' 
                   AND table_schema = @db_name
                   AND column_name = 'created_at');
SET @sqlstmt = IF(@col_exists = 0, 
    'ALTER TABLE teams ADD COLUMN created_at TIMESTAMP NULL DEFAULT NULL', 
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Проверка и обновление таблицы rating
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_name = 'rating' 
                     AND table_schema = @db_name);

-- Проверка существования столбца player_id в таблице rating
SET @player_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = 'rating' 
                        AND table_schema = @db_name
                        AND column_name = 'player_id');

-- Проверка существования столбца user_id в таблице rating
SET @user_id_exists = (SELECT COUNT(*) FROM information_schema.columns 
                      WHERE table_name = 'rating' 
                      AND table_schema = @db_name
                      AND column_name = 'user_id');

-- Если таблица существует и есть столбец user_id, удаляем его
DROP PROCEDURE IF EXISTS remove_user_id_from_rating;
DELIMITER //
CREATE PROCEDURE remove_user_id_from_rating()
BEGIN
    IF @table_exists > 0 AND @user_id_exists > 0 THEN
        -- Проверяем, есть ли player_id, чтобы не потерять данные
        IF @player_id_exists = 0 THEN
            -- Если player_id не существует, сначала добавляем его и копируем данные
            ALTER TABLE rating ADD COLUMN player_id int(11) UNSIGNED NOT NULL AFTER user_id;
            UPDATE rating SET player_id = user_id;
            ALTER TABLE rating ADD INDEX rating_player_id_index (player_id);
        END IF;
        
        -- Проверяем наличие индексов, которые могут использовать user_id
        SET @index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                            WHERE table_name = 'rating' 
                            AND table_schema = @db_name
                            AND index_name = 'rating_user_game_club_season_unique');
                            
        -- Проверяем, существует ли уже новый индекс с player_id
        SET @new_index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                                WHERE table_name = 'rating' 
                                AND table_schema = @db_name
                                AND index_name = 'rating_player_game_club_season_unique');
        
        -- Если есть уникальный индекс с user_id и нет нового индекса, создаем новый с player_id
        IF @index_exists > 0 AND @new_index_exists = 0 THEN
            -- Создаем новый индекс с player_id вместо user_id
            ALTER TABLE rating 
            DROP INDEX rating_user_game_club_season_unique,
            ADD UNIQUE INDEX rating_player_game_club_season_unique (player_id, game_id, club_id, season_id);
        ELSEIF @index_exists > 0 AND @new_index_exists > 0 THEN
            -- Если оба индекса существуют, просто удаляем старый
            ALTER TABLE rating DROP INDEX rating_user_game_club_season_unique;
        END IF;
        
        -- Удаляем индекс для user_id, если он существует
        SET @user_id_index_exists = (SELECT COUNT(*) FROM information_schema.statistics 
                                    WHERE table_name = 'rating' 
                                    AND table_schema = @db_name
                                    AND index_name = 'rating_user_id_index');
                                    
        IF @user_id_index_exists > 0 THEN
            ALTER TABLE rating DROP INDEX rating_user_id_index;
        END IF;
        
        -- Теперь можно безопасно удалить столбец
        ALTER TABLE rating DROP COLUMN user_id;
        SELECT 'Удален столбец user_id из таблицы rating' AS message;
    END IF;
END //
DELIMITER ;

CALL remove_user_id_from_rating();
DROP PROCEDURE IF EXISTS remove_user_id_from_rating;













