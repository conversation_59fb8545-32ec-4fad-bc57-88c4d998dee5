import './bootstrap';
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';


window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: import.meta.env.VITE_PUSHER_APP_KEY,
    cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER,
    forceTLS: true
});

console.log('Echo initialized:', window.Echo);

window.Echo.connector.pusher.connection.bind('connected', function() {
    console.log('Connected to Pusher!');
});

// Подписка на канал
console.log('Subscribing to match-channel');
window.Echo.channel('match-channel')
    .listen('MatchFound', (data) => {
        console.log('Match found:', data);
        console.log('Pusher message:', data.message); // Добавлено для отладки
        alert(data.message);
    });