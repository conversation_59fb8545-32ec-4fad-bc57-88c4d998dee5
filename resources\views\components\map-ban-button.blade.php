@props(['mapId', 'bannedMaps', 'currentVotingTeam', 'teamId'])

@if(in_array($mapId, $bannedMaps))
    <button type="button" class="btn btn-success btn-sm w-100" wire:click="unbanMap({{ $mapId }})">
        Разбанить
    </button>
@else
    <button type="button" class="btn btn-danger btn-sm w-100" 
        wire:click="banMap({{ $mapId }})"
        wire:loading.attr="disabled"
        wire:target="banMap({{ $mapId }})"
        {{ $currentVotingTeam && (int)$currentVotingTeam->id !== (int)$teamId ? 'disabled' : '' }}>
        <span wire:loading.remove wire:target="banMap({{ $mapId }})">Забанить</span>
        <span wire:loading wire:target="banMap({{ $mapId }})">
            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
        </span>
    </button>
@endif