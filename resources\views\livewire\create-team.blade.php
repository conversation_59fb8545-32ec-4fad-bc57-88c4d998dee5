<div>
    @section('title', 'Создание команды')
    <div class="container py-4">        
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-body">
                        <a href="{{ route('team') }}" class="btn btn-sm btn-outline-primary" wire:navigate="">
                            <i class="ri-arrow-left-line me-1"></i> Вернуться
                        </a>
                        
                        <div wire:loading wire:target="updateSelectedGame" class="text-center my-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Загрузка...</span>
                            </div>
                        </div>
                        
                        <livewire:carousel />
                        
                        @include('livewire.notification')

                        @if($selectedGameId)
                            
                            @if(count($userTeams) > 0)
                                <div class="mt-4">
                                    <h4 class="text-center mb-4">Ваша команда в игре {{ $gameName }}</h4>
                                    
                                    @foreach($userTeams as $team)
                                        <div class="card mb-4">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">{{ $team->name }}
                                                    @if($team->captain)
                                                        <small class="text-muted">(Капитан: {{ $team->captain->client_nick }})</small>
                                                    @endif
                                                </h5>
                                                <span class="badge bg-primary">{{ auth()->user()->getRoleInTeam($team->id) }}</span>
                                            </div>
                                            <div class="card-body">
                                                @if($team->description)
                                                    <p class="card-text">{{ $team->description }}</p>
                                                    <hr>
                                                @endif

                                                <h6 class="mb-3">Статистика команды</h6>
                                                <div class="row mb-4">
                                                    <div class="col-md-4 text-center">
                                                        <div class="card bg-light">
                                                            <div class="card-body">
                                                                <h3>{{ $team->rating ?? 0 }}</h3>
                                                                <small>Рейтинг</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4 text-center">
                                                        <div class="card bg-light">
                                                            <div class="card-body">
                                                                <h3>{{ $teamStats[$team->id]['matches'] ?? 0 }}</h3>
                                                                <small>Матчей</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4 text-center">
                                                        <div class="card bg-light">
                                                            <div class="card-body">
                                                                <h3>{{ $teamStats[$team->id]['wins'] ?? 0 }}</h3>
                                                                <small>Побед</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <h6 class="mb-3">Участники команды</h6>
                                                <ul class="list-group">
                                                    @foreach($teamMembers[$team->id] as $member)
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <i class="ri-user-line me-2"></i>
                                                                {{ $member->user->client_nick }}
                                                            </div>
                                                            <div class="d-flex align-items-center">
                                                                <span class="badge {{ $member->role == 'captain' ? 'bg-danger' : 'bg-info' }} rounded-pill me-2">
                                                                    {{ $member->role == 'captain' ? 'Капитан' : 'Участник' }}
                                                                </span>
                                                                
                                                                @if(auth()->user()->isCaptainOf($team->id) && $member->role != 'captain')
                                                                    <button class="btn btn-sm btn-outline-danger" 
                                                                            wire:click="$dispatch('open-kick-confirm-modal', { teamId: {{ $team->id }}, userId: {{ $member->player_id }} })">
                                                                        <i class="ri-delete-bin-line"></i>
                                                                    </button>
                                                                @endif
                                                            </div>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                                
                                                @if(auth()->user()->isCaptainOf($team->id))
                                                    <h6 class="mb-3 mt-4">Управление командой</h6>
                                                    
                                                    @if(isset($pendingRequests[$team->id]) && count($pendingRequests[$team->id]) > 0)
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-light">
                                                                <h6 class="mb-0">Заявки на вступление</h6>
                                                            </div>
                                                            <ul class="list-group list-group-flush">
                                                                @foreach($pendingRequests[$team->id] as $request)
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                        <div>
                                                                            <i class="ri-user-follow-line me-2"></i>
                                                                            {{ $request->user->client_nick }}
                                                                            <span class="badge bg-info text-dark ms-2">Хочет вступить</span>
                                                                        </div>
                                                                        <div>
                                                                            <button class="btn btn-sm btn-success me-2" wire:click="acceptRequest({{ $request->id }})">
                                                                                <i class="ri-check-line"></i> Принять
                                                                            </button>
                                                                            <button class="btn btn-sm btn-danger" wire:click="rejectRequest({{ $request->id }})">
                                                                                <i class="ri-close-line"></i> Отклонить
                                                                            </button>
                                                                        </div>
                                                                    </li>
                                                                @endforeach
                                                            </ul>
                                                        </div>
                                                    @endif

                                                    @if(isset($pendingInvitations[$team->id]) && count($pendingInvitations[$team->id]) > 0)
                                                        <div class="card mb-3">
                                                            <div class="card-header bg-light">
                                                                <h6 class="mb-0">Приглашенные игроки</h6>
                                                            </div>
                                                            <ul class="list-group list-group-flush">
                                                                @foreach($pendingInvitations[$team->id] as $invitation)
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                                        <div>
                                                                            <i class="ri-user-add-line me-2"></i>
                                                                            {{ $invitation->user->client_nick }}
                                                                            <span class="badge bg-warning text-dark ms-2">Ожидает ответа</span>
                                                                        </div>
                                                                        <button class="btn btn-sm btn-outline-danger" wire:click="cancelInvitation({{ $invitation->id }})">
                                                                            <i class="ri-close-line"></i> Отменить
                                                                        </button>
                                                                    </li>
                                                                @endforeach
                                                            </ul>
                                                        </div>
                                                    @endif

                                                    <div class="d-flex justify-content-end gap-2 mt-3">
                                                        @php
                                                            $requiredSize = $team->game_id == 3 ? 4 : 5; // 4 для PUBG, 5 для CS2 и Dota2
                                                            $isTeamFull = count($teamMembers[$team->id]) >= $requiredSize;
                                                        @endphp
                                                        @if(auth()->user()->isCaptainOf($team->id))
                                                            <div class="position-relative d-inline-block" 
                                                                x-data="{showTooltip: false}"
                                                                @mouseenter="showTooltip = true" 
                                                                @mouseleave="showTooltip = false"
                                                                @focus="showTooltip = true"
                                                                @blur="showTooltip = false">
                                                                <button type="button" class="btn btn-success" 
                                                                    wire:click="createNewMatch({{ $team->id }})"
                                                                    @if(!$isTeamFull) disabled @endif>
                                                                    <i class="ri-game-line me-1"></i> Создать новый матч
                                                                </button>
                                                                @if(!$isTeamFull)
                                                                    <div x-show="showTooltip" class="tooltip-alpine" x-transition>Для создания матча необходимо {{ $requiredSize }} игроков в команде</div>
                                                                @endif
                                                            </div>
                                                            <div class="position-relative d-inline-block" 
                                                                x-data="{showTooltip: false}"
                                                                @mouseenter="showTooltip = true" 
                                                                @mouseleave="showTooltip = false"
                                                                @focus="showTooltip = true"
                                                                @blur="showTooltip = false">
                                                                <button class="btn btn-secondary" 
                                                                    wire:click="findNewMatch({{ $team->id }})"
                                                                    @if(!$isTeamFull) disabled @endif>
                                                                    <i class="ri-user-add-line me-1"></i> Найти соперника
                                                                </button>
                                                                @if(!$isTeamFull)
                                                                    <div x-show="showTooltip" class="tooltip-alpine" x-transition>Для поиска соперника необходимо {{ $requiredSize }} игроков в команде</div>
                                                                @endif
                                                            </div>
                                                            @if(config('app.debug'))
                                                            <button class="btn btn-warning" 
                                                                wire:click="$dispatch('open-create-test-match-modal', { teamId: {{ $team->id }} })">
                                                                <i class="ri-bug-line me-1"></i> Тестовый матч
                                                            </button>
                                                            @endif
                                                            @if(!$isTeamFull)
                                                                <button class="btn btn-primary" wire:click="invitePlayer({{ $team->id }})">
                                                                    <i class="ri-user-add-line me-1"></i> Пригласить игрока
                                                                </button>
                                                            @endif
                                                            <button class="btn btn-danger" 
                                                                    wire:click="$dispatch('open-disband-confirm-modal', { teamId: {{ $team->id }} })">
                                                                <i class="ri-delete-bin-line me-1"></i> Расформировать команду
                                                            </button>
                                                        @endif
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="mt-4 text-center">
                                    <p>У вас нет команды в игре {{ $gameName }}.</p>
                                    <div class="d-flex justify-content-center gap-2">
                                        <button wire:click="findTeam" class="btn btn-primary">Найти команду</button>
                                        <button type="button" class="btn btn-success" @click="$dispatch('open-create-team-modal')">
                                            Создать команду
                                        </button>
                                    </div>
                                </div>
                            @endif
    
                            @if(session('error'))
                                <div class="alert alert-danger mt-3 text-center">
                                    {{ session('error') }}
                                </div>
                            @endif
                        @endif
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    @if(count($receivedInvitations) > 0)
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">Приглашения в команды</h6>
            </div>
            <ul class="list-group list-group-flush">
                @foreach($receivedInvitations as $invitation)
                    <li class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ $invitation->team->name }}</strong>
                                <span class="text-muted ms-2">Капитан: {{ $invitation->team->captain->client_nick }}</span>
                            </div>
                            <div>
                                <button class="btn btn-sm btn-success me-2" wire:click="acceptInvitation({{ $invitation->id }})">
                                    <i class="ri-check-line me-1"></i> Принять
                                </button>
                                <button class="btn btn-sm btn-danger" wire:click="declineInvitation({{ $invitation->id }})">
                                    <i class="ri-close-line me-1"></i> Отклонить
                                </button>
                            </div>
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    @endif
    
    <livewire:modals.create-team-modal :selectedGameId="$selectedGameId" />

    <livewire:modals.invite-player-modal />

    <livewire:modals.create-match-modal />

    <livewire:modals.create-test-match-modal />

    <script>
        document.addEventListener('livewire:initialized', () => {
            Livewire.on('closeInviteModal', () => {
                window.dispatchEvent(new CustomEvent('closeInviteModal'));
            });
            
            Livewire.on('closeCreateTeamModal', () => {
                window.dispatchEvent(new CustomEvent('closeCreateTeamModal'));
            });

            Livewire.on('showCreateNotification', (data) => {
                window.dispatchEvent(new CustomEvent('showNotification', { detail: data }));
            });
        });
    </script>
    @if($isLoading)
        <div class="d-flex justify-content-center my-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Загрузка...</span>
            </div>
            <span class="ms-2">Загрузка данных...</span>
        </div>
    @endif
    
    <div x-data="{ 
            open: false,
            teamId: null,
            userId: null,
            init() {
                window.addEventListener('open-kick-confirm-modal', (e) => {
                    this.teamId = e.detail.teamId;
                    this.userId = e.detail.userId;
                    this.open = true;
                    document.body.classList.add('modal-open');
                });
                
                this.$watch('open', value => {
                    if (!value) {
                        document.body.classList.remove('modal-open');
                    }
                });
            }
        }"
        @keydown.escape.window="open = false"
        class="modal fade"
        :class="{ 'show': open }"
        style="display: none;"
        :style="open ? 'display: block;' : 'display: none;'"
        tabindex="-1"
        aria-hidden="true"
        wire:ignore.self>
        
        <div class="modal-backdrop fade" 
             :class="{ 'show': open }"
             x-show="open" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="open = false"></div>
             
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Подтверждение исключения</h5>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Вы уверены, что хотите исключить этого участника из команды?</p>
                    <p class="text-danger">Это действие нельзя отменить.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @click="open = false">Отмена</button>
                    <button type="button" class="btn btn-danger" 
                            @click="open = false; $wire.kickMember(teamId, userId)">
                        Исключить
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div x-data="{ 
            open: false,
            teamId: null,
            init() {
                window.addEventListener('open-disband-confirm-modal', (e) => {
                    this.teamId = e.detail.teamId;
                    this.open = true;
                    document.body.classList.add('modal-open');
                });
                
                this.$watch('open', value => {
                    if (!value) {
                        document.body.classList.remove('modal-open');
                    }
                });
            }
        }"
        @keydown.escape.window="open = false"
        class="modal fade"
        :class="{ 'show': open }"
        style="display: none;"
        :style="open ? 'display: block;' : 'display: none;'"
        tabindex="-1"
        aria-hidden="true"
        wire:ignore.self>
        
        <div class="modal-backdrop fade" 
             :class="{ 'show': open }"
             x-show="open" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="open = false"></div>
             
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Подтверждение расформирования</h5>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Вы уверены, что хотите расформировать команду?</p>
                    <p class="text-danger">Это действие нельзя отменить. Все участники будут удалены из команды.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @click="open = false">Отмена</button>
                    <button type="button" class="btn btn-danger" 
                            @click="open = false; $wire.disbandTeam(teamId)">
                        Расформировать
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('livewire:initialized', () => {
            // ... existing code ...
            
            // Обработчик для кнопки расформирования команды
            Livewire.on('disbandTeam', (teamId) => {
                window.dispatchEvent(new CustomEvent('open-disband-confirm-modal', {
                    detail: { teamId: teamId }
                }));
            });
        });
    </script>

    <style>
        /* Стили для модального окна */
        .modal.show {
            display: block;
            overflow-x: hidden;
            overflow-y: auto;
        }
        
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #000;
            z-index: 1040;
        }
        
        .modal-backdrop.show {
            opacity: 0.5;
        }
        
        .modal-dialog {
            position: relative;
            width: auto;
            margin: 1.75rem auto;
            max-width: 500px;
            z-index: 1050;
        }
        
        .modal-dialog.modal-xl {
            max-width: 1200px;
        }
        
        body.modal-open {
            overflow: hidden;
            padding-right: 17px; /* Компенсация ширины полосы прокрутки */
        }
        [x-tooltip] {
        position: relative;
        }
        [x-tooltip]:before {
            content: attr(x-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.2s, visibility 0.2s;
            z-index: 1000;
        }
        [x-tooltip]:hover:before {
            visibility: visible;
            opacity: 1;
        }
        .tooltip-alpine {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 5px 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            margin-bottom: 5px;
            pointer-events: none;
        }

        .tooltip-alpine::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border-width: 5px;
            border-style: solid;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
        }
    </style>
</div>

