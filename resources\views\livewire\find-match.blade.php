<div>
    <!-- Контейнер для push-уведомлений -->
    <div id="notification-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
    <!-- Включаем шаблон с уведомлениями напрямую -->
    @include('livewire.notification')
    </div>
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <h5 class="mb-0 me-3">Поиск матча</h5>
            </div>
            <button class="btn btn-sm btn-outline-primary" wire:click="backToTeam">
                <i class="ri-arrow-left-line me-1"></i> Вернуться к команде
            </button>
        </div>
        <div class="card-body">
            @if($searchStatus === 'error')
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @else
                <div class="row">
                    <div class="col-md-12">
                        @livewire('match-search', ['teamId' => $teamId, 'team' => $team, 'searchStatus' => $searchStatus, 'foundMatch' => $foundMatch])
                    </div>
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Ваша команда</h6>
                            </div>
                            <div class="card-body">
                                <h5>{{ $team->name }}</h5>
                                <p class="text-muted">{{ $team->description }}</p>
                                
                                @if($team && $team->members && count($team->members) > 0)
                                <div class="mt-3">
                                    <h6>Состав команды:</h6>
                                    <ul class="list-group">
                                        @foreach($team->members as $member)
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    @if(isset($member->user->avatar) && $member->user->avatar)
                                                        <img src="{{ asset($member->user->avatar) }}" class="rounded-circle me-2" width="30" height="30" alt="{{ $member->user->client_nick ?? 'User' }}">
                                                    @else
                                                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                            {{ substr($member->user->client_nick ?? 'U', 0, 1) }}
                                                        </div>
                                                    @endif
                                                    {{ $member->user->client_nick ?? 'Unknown User' }}
                                                </div>
                                                <span class="badge bg-primary rounded-pill">
                                                    {{ ($member->role ?? 'member') === 'captain' ? 'Капитан' : 'Игрок' }}
                                                </span>
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                                @else
                                <div class="mt-3">
                                    <div class="alert alert-warning">
                                        <i class="ri-information-line me-1"></i>
                                        Информация о составе команды недоступна
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>                    
                    
                </div>
            @endif
        </div>
    </div>
