<div wire:poll.2000ms="refreshMatchData" class="live-match-manager">
    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session()->has('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(!$match)
        <div class="text-center py-4">
            <i class="ri-error-warning-line fs-1 text-warning"></i>
            <h5 class="mt-3">Матч не найден</h5>
            <p class="text-muted">Матч с ID {{ $matchId }} не существует или был удален.</p>
        </div>
    @else
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="ri-gamepad-line me-2"></i>
                    Матч #{{ $match->id }}
                    <span class="badge bg-{{ $matchStatus === 'ready_check' ? 'warning' : ($matchStatus === 'map_voting' ? 'info' : 'success') }} ms-2">
                        {{ $matchStatus === 'ready_check' ? 'Подтверждение готовности' : ($matchStatus === 'map_voting' ? 'Голосование по картам' : 'Игра') }}
                    </span>
                </h5>
            </div>
            <div class="card-body">
                <!-- Информация о командах -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card h-100 {{ $userTeam && $userTeam->id === $match->team1_id ? 'border-primary' : '' }}">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="ri-team-line me-1"></i>
                                    {{ $match->team1->name }}
                                    @if($userTeam && $userTeam->id === $match->team1_id)
                                        <span class="badge bg-primary ms-2">Ваша команда</span>
                                    @endif
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <p class="text-muted mb-1">Рейтинг: {{ $match->team1->rating ?? 0 }}</p>
                                @if($matchStatus === 'ready_check')
                                    @if($readyStatus['user_team'] && $userTeam && $userTeam->id === $match->team1_id)
                                        <span class="badge bg-success">Готовы</span>
                                    @elseif($readyStatus['opponent_team'] && $opponentTeam && $opponentTeam->id === $match->team1_id)
                                        <span class="badge bg-success">Готовы</span>
                                    @else
                                        <span class="badge bg-warning">Ожидание</span>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100 {{ $userTeam && $userTeam->id === $match->team2_id ? 'border-primary' : '' }}">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="ri-team-line me-1"></i>
                                    {{ $match->team2->name }}
                                    @if($userTeam && $userTeam->id === $match->team2_id)
                                        <span class="badge bg-primary ms-2">Ваша команда</span>
                                    @endif
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <p class="text-muted mb-1">Рейтинг: {{ $match->team2->rating ?? 0 }}</p>
                                @if($matchStatus === 'ready_check')
                                    @if($readyStatus['user_team'] && $userTeam && $userTeam->id === $match->team2_id)
                                        <span class="badge bg-success">Готовы</span>
                                    @elseif($readyStatus['opponent_team'] && $opponentTeam && $opponentTeam->id === $match->team2_id)
                                        <span class="badge bg-success">Готовы</span>
                                    @else
                                        <span class="badge bg-warning">Ожидание</span>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Статус подтверждения готовности -->
                @if($matchStatus === 'ready_check')
                    <div class="text-center mb-4">
                        <div class="alert {{ $readyStatus['user_team'] ? 'alert-success' : 'alert-warning' }}">
                            @if($readyStatus['user_team'])
                                <i class="ri-check-double-line me-1"></i>
                                Ваша команда готова к игре
                            @else
                                <i class="ri-time-line me-1"></i>
                                Ожидание подтверждения готовности
                            @endif
                        </div>
                        
                        @if(!$readyStatus['user_team'])
                            <button class="btn btn-success btn-lg" wire:click="confirmReady" wire:loading.attr="disabled">
                                <span wire:loading.remove wire:target="confirmReady">
                                    <i class="ri-check-line me-1"></i>
                                    Подтвердить готовность
                                </span>
                                <span wire:loading wire:target="confirmReady">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    Подтверждение...
                                </span>
                            </button>
                        @endif
                    </div>
                @endif

                <!-- Голосование по картам -->
                @if($matchStatus === 'map_voting')
                    <div class="mb-4">
                        <div class="alert {{ $isUserTurn ? 'alert-success' : 'alert-info' }}">
                            @if($isUserTurn)
                                <i class="ri-arrow-right-circle-fill me-1"></i>
                                Ваш ход! Выберите карту для бана.
                            @else
                                <i class="ri-time-line me-1"></i>
                                Ожидание хода соперника...
                            @endif
                        </div>

                        <!-- Доступные карты -->
                        <h6 class="mb-3">Доступные карты:</h6>
                        <div class="row">
                            @foreach($this->getAvailableMapsForVoting() as $map)
                                <div class="col-md-4 col-lg-3 mb-3">
                                    <div class="card h-100 position-relative">
                                        @if($map->image_url)
                                            <img src="{{ asset($map->image_url) }}" class="card-img-top" alt="{{ $map->name }}" style="height: 120px; object-fit: cover;">
                                        @else
                                            <div class="card-img-top bg-secondary text-white d-flex align-items-center justify-content-center" style="height: 120px;">
                                                <i class="ri-map-pin-line fs-1"></i>
                                            </div>
                                        @endif
                                        <div class="card-body">
                                            <h6 class="card-title text-center">{{ $map->name }}</h6>
                                            <button type="button" class="btn btn-danger btn-sm w-100" 
                                                wire:click="banMap({{ $map->id }})"
                                                wire:loading.attr="disabled"
                                                {{ !$isUserTurn ? 'disabled' : '' }}>
                                                <span wire:loading.remove wire:target="banMap({{ $map->id }})">
                                                    <i class="ri-close-line me-1"></i>
                                                    Забанить
                                                </span>
                                                <span wire:loading wire:target="banMap({{ $map->id }})">
                                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Забаненные карты -->
                        @if(count($bannedMaps) > 0)
                            <hr>
                            <h6 class="mb-3">Забаненные карты:</h6>
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($this->getBannedMapsInfo() as $map)
                                    <span class="badge bg-danger fs-6">
                                        <i class="ri-close-circle-line me-1"></i>
                                        {{ $map->name }}
                                    </span>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endif

                <!-- Статус игры -->
                @if($matchStatus === 'live')
                    <div class="text-center py-4">
                        <i class="ri-gamepad-fill fs-1 text-success"></i>
                        <h5 class="mt-3">Игра началась!</h5>
                        <p class="text-muted">Выбранная карта: 
                            @if($match->selected_map_id)
                                @php
                                    $selectedMap = \App\Models\Cs2Map::find($match->selected_map_id);
                                @endphp
                                <strong>{{ $selectedMap ? $selectedMap->name : 'Неизвестная карта' }}</strong>
                            @else
                                <strong>Не выбрана</strong>
                            @endif
                        </p>
                        <div class="alert alert-info">
                            <i class="ri-information-line me-1"></i>
                            Данные для подключения к серверу будут предоставлены в ближайшее время.
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div> 