<div>
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h3>Бан карт для матча #{{ $match->id }}</h3>
            <p class="text-muted">Выберите до {{ $maxBans }} карт для бана</p>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Доступные карты</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($availableMaps as $map)
                            @if(!in_array($map->id, $bannedMaps))
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        @if($map->image_url)
                                            <img src="{{ asset($map->image_url) }}" 
                                                 class="card-img-top" 
                                                 alt="{{ $map->name }}">
                                        @endif
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $map->name }}</h6>
                                            <button class="btn btn-danger btn-sm w-100" 
                                                    wire:click="banMap({{ $map->id }})"
                                                    wire:loading.attr="disabled">
                                                <span wire:loading.remove wire:target="banMap({{ $map->id }})">
                                                    Забанить
                                                </span>
                                                <span wire:loading wire:target="banMap({{ $map->id }})">
                                                    <span class="spinner-border spinner-border-sm"></span>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Забаненные карты</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($availableMaps as $map)
                            @if(in_array($map->id, $bannedMaps))
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        @if($map->image_url)
                                            <img src="{{ asset('maps/' . $map->image_url) }}" 
                                                 class="card-img-top" 
                                                 alt="{{ $map->name }}">
                                        @endif
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $map->name }}</h6>
                                            <button class="btn btn-success btn-sm w-100" 
                                                    wire:click="unbanMap({{ $map->id }})"
                                                    wire:loading.attr="disabled">
                                                <span wire:loading.remove wire:target="unbanMap({{ $map->id }})">
                                                    Разбанить
                                                </span>
                                                <span wire:loading wire:target="unbanMap({{ $map->id }})">
                                                    <span class="spinner-border spinner-border-sm"></span>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


    <div class="card">
        <div class="card-header bg-light">
            <h6 class="mb-0">Выбор карт</h6>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6 text-center">
                    <h6>Ваша команда</h6>
                    <h5>{{ $team->name }}</h5>
                </div>
                <div class="col-md-6 text-center">
                    <h6>Команда соперников</h5>
                    <h5>{{ $opponents->name ?? 'Неизвестная команда' }}</h5>
                </div>
            </div>
            
            <!-- Остальная часть шаблона остается без изменений -->
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('livewire:init', () => {
        Echo.private('captain.' + {{ auth()->id() }})
            .listen('.map.banned', (e) => {
                console.log('Map banned event:', e);
                
                if (parseInt(e.toCaptainId) === parseInt({{ auth()->id() }})) {
                    Livewire.dispatch('map.banned', {
                        matchId: e.matchId,
                        mapId: e.mapId,
                        fromCaptainId: e.fromCaptainId,
                        toCaptainId: e.toCaptainId,
                        nextVotingTeamId: e.nextVotingTeamId
                    });
                    
                    const audio = new Audio('/sounds/notification.mp3');
                    audio.play().catch(err => console.log('Audio error:', err));
                }
            });
    });
</script>
@endpush