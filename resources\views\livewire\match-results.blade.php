<div>
    <div class="container">
        <h1>Результаты матчей</h1>
        
        <div class="card mb-4">
            <div class="card-header">Фильтры</div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="gameType" class="form-label">Тип игры</label>
                        <select id="gameType" class="form-select" wire:model.live="gameType">
                            {{-- <option value="all">Все игры</option> --}}
                            <option value="1" selected>CS2</option>
                            <option value="2">Dota 2</option>
                            <option value="3">PUBG</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="dateFrom" class="form-label">Дата от</label>
                        <input type="date" id="dateFrom" class="form-control" wire:model.live="dateFrom">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="dateTo" class="form-label">Дата до</label>
                        <input type="date" id="dateTo" class="form-control" wire:model.live="dateTo">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="resultStatus" class="form-label">Результат</label>
                        <select id="resultStatus" class="form-select" wire:model.live="resultStatus">
                            <option value="all">Все результаты</option>
                            <option value="victory">Победы</option>
                            <option value="defeat">Поражения</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">Список матчей</div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Тип игры</th>
                                <th>Счет</th>
                                <th>Результат</th>
                                <th>Дата</th>
                                <th>Действия</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($matches as $match)
                                <tr>
                                    <td>{{ $match->id }}</td>
                                    <td>{{ $getGameName($match->game_type) }}</td>
                                    <td>{{ $match->match_score ?? 'Н/Д' }}</td>
                                    <td>
                                        @if($match->result)
                                            @if($match->result->victory)
                                                <span class="badge bg-success">Победа</span>
                                            @else
                                                <span class="badge bg-danger">Поражение</span>
                                            @endif
                                        @else
                                            <span class="badge bg-warning">Ожидает</span>
                                        @endif
                                    </td>
                                    <td>{{ $match->date?->format('d.m.Y H:i') }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" wire:click="showDetails({{ $match->id }})">
                                            Детали
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="7" class="text-center">Нет данных о матчах</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    {{ $matches->links() }}
                </div>
            </div>
        </div>
        
        @if($selectedMatch)
            <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1" aria-modal="true" role="dialog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Детали матча #{{ $selectedMatch->id }}</h5>
                            <button type="button" class="btn-close" wire:click="closeDetails"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <h6>Информация о матче</h6>
                                    <table class="table">
                                        <tr>
                                            <th>ID:</th>
                                            <td>{{ $selectedMatch->id }}</td>
                                        </tr>
                                        <tr>
                                            <th>Клуб:</th>
                                            <td>{{ $selectedMatch->club_id }}</td>
                                        </tr>
                                        <tr>
                                            <th>Счет матча:</th>
                                            <td>{{ $selectedMatch->math_score }}</td>
                                        </tr>
                                        <tr>
                                            <th>ID матча:</th>
                                            <td>@if($selectedMatch->victory)
                                                <span class="badge bg-success">Победа</span>
                                            @else
                                                <span class="badge bg-danger">Поражение</span>
                                            @endif</td>
                                        </tr>
                                        <tr>
                                            <th>Счет:</th>
                                            <td>{{ $selectedMatch->score ?? 'Н/Д' }}</td>
                                        </tr>
                                        <tr>
                                            <th>Дата создания:</th>
                                            <td>{{ $selectedMatch->date?->format('d.m.Y H:i') }}</td>
                                        </tr>
                                    </table>
                                </div>
                                
                                {{-- <div class="col-md-6">
                                    <h6>Результат матча</h6>
                                    @if($selectedMatch->result)
                                        <table class="table">
                                            <tr>
                                                <th>Результат:</th>
                                                <td>
                                                    @if($selectedMatch->victory)
                                                        <span class="badge bg-success">Победа</span>
                                                    @else
                                                        <span class="badge bg-danger">Поражение</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <th>Очки:</th>
                                                <td>{{ $selectedMatch->result->add_score }}</td>
                                            </tr>
                                            
                                            @if($selectedMatch->result->details)
                                                @php $details = is_array($selectedMatch->result->details) ? $selectedMatch->result->details : json_decode($selectedMatch->result->details, true); @endphp
                                                @if(is_array($details))
                                                    @foreach($details as $key => $value)
                                                        <tr>
                                                            <th>{{ ucfirst($key) }}:</th>
                                                            <td>{{ is_array($value) ? json_encode($value) : $value }}</td>
                                                        </tr>
                                                    @endforeach
                                                @endif
                                            @endif
                                        </table>
                                    @else
                                        <p class="text-muted">Нет результатов для этого матча</p>
                                    @endif
                                </div> --}}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" wire:click="closeDetails">Закрыть</button>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

