<div wire:poll.1000ms="updateSearchTime"
    x-data="{
        searchTime: 0,
        interval: null,
        searchStatus: @entangle('searchStatus'),
        showAcceptModal: @entangle('showAcceptModal'), // <-- ИСПРАВЛЕНО: Привязываем к свойству Livewire
        matchData: @entangle('matchData'), // <-- ИСПРАВЛЕНО: Привязываем к свойству Livewire
        
        // toggleAcceptModal метод становится менее критичным для внешнего управления,
        // так как x-show будет напрямую реагировать на showAcceptModal
        // Однако его можно оставить для использования внутри Alpine, если это нужно.
        // Я удалю его для ясности, так как он больше не нужен для реакции на Livewire-события.
        /* toggleAcceptModal(show = true, data = null) {
            console.log('toggleAcceptModal called with:', show, data);
            this.showAcceptModal = show;
            this.matchData = data;
            if (show) {
                document.body.classList.add('modal-open');
            } else {
                document.body.classList.remove('modal-open');
            }
            console.log('Modal state after toggle:', this.showAcceptModal);
        } */
    }"
    x-init="$nextTick(() => {
        console.log('Alpine component initialized');
        // Слушатель для воспроизведения звука, если его диспатчим из Livewire PHP
        // Это остается, так как звук - это побочный эффект, не связанный напрямую с видимостью модала.
        Livewire.on('playNotificationSound', () => {
            const audio = new Audio('/sounds/zvonkoe-uvedomlenie-ob-sms.mp3');
            audio.play().catch(err => console.log('Audio playback blocked or error:', err));
        });
    })"
>
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0">Статус поиска {{ $searchStatus }}</h6>
        </div>
        <div class="card-body">
            <div wire:init="loadComponent" wire:loading.delay.longest class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Загрузка...</span>
                </div>
                <p class="mt-2">Загрузка компонента...</p>
            </div>

            <div wire:loading.remove.delay.longest>
                @if($searchStatus === 'waiting')
                    <div class="text-center py-4">
                        <i class="ri-search-line fs-1"></i>
                        <h5 class="mt-3">Готовы к поиску матча</h5>
                        <p class="text-muted">Нажмите кнопку "Начать поиск" для поиска соперников</p>
                        <div class="d-flex justify-content-center gap-2">
                            <button class="btn btn-primary btn-lg mt-3" wire:click="startSearch" wire:loading.attr="disabled">
                                <span wire:loading.remove wire:target="startSearch"><i class="ri-search-line me-1"></i> Начать поиск</span>
                                <span wire:loading wire:target="startSearch">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    Поиск...
                                </span>
                            </button>
                        </div>
                    </div>
                @elseif($searchStatus === 'searching')
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Загрузка...</span>
                        </div>
                        <h5 class="mt-3">Поиск матча...</h5>
                        <p class="text-muted">Статус поиска: {{ $searchStatus }}</p>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                                style="width: {{ min(100, ($searchTime / $maxSearchTime) * 100) }}%"
                                aria-valuenow="{{ $searchTime }}" aria-valuemin="0" aria-valuemax="{{ $maxSearchTime }}"></div>
                        </div>
                        <div class="d-flex justify-content-center gap-2">
                            <button class="btn btn-danger mt-3" wire:click="cancelSearch" wire:loading.attr="disabled">
                                <span wire:loading.remove wire:target="cancelSearch"><i class="ri-close-line me-1"></i> Отменить поиск</span>
                                <span wire:loading wire:target="cancelSearch">
                                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                    Отмена...
                                </span>
                            </button>
                        </div>
                    </div>
                @elseif($searchStatus === 'ready_check')
                    <div class="text-center py-4">
                        <i class="ri-trophy-line fs-1 text-success"></i>
                        <h5 class="mt-3">Матч найден!</h5>
                        
                        <div class="card mt-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Информация о матче</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 text-center">
                                        <h6>Ваша команда</h6>
                                        <h5>{{ $team->name }}</h5>
                                        <p class="text-muted">Рейтинг: {{ $team->rating ?? 0 }}</p>
                                        @if($foundMatch && method_exists($foundMatch, 'isTeamReady') && $foundMatch->isTeamReady($team->id))
                                            <span class="badge bg-success">Готовы</span>
                                        @else
                                            <span class="badge bg-warning">Не готовы</span>
                                        @endif
                                    </div>
                                    <div class="col-md-6 text-center">
                                        <h6>Команда соперников</h6>
                                        <h5>{{ $opponents['team']->name ?? 'Неизвестная команда' }}</h5>
                                        <p class="text-muted">Рейтинг: {{ $opponents['team']->rating ?? 0 }}</p>
                                        @php
                                            $opponentTeamId = $foundMatch->team1_id == $teamId ? $foundMatch->team2_id : $foundMatch->team1_id;
                                        @endphp
                                        @if($foundMatch && method_exists($foundMatch, 'isTeamReady') && $foundMatch->isTeamReady($opponentTeamId))
                                            <span class="badge bg-success">Готовы</span>
                                        @else
                                            <span class="badge bg-warning">Не готовы</span>
                                        @endif
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-center gap-2 mt-4">
                                    @if($foundMatch && method_exists($foundMatch, 'isTeamReady') && !$foundMatch->isTeamReady($teamId))
                                        <button class="btn btn-success" wire:click="acceptMatch" wire:loading.attr="disabled">
                                            <span wire:loading.remove wire:target="acceptMatch"><i class="ri-check-line me-1"></i> Принять матч</span>
                                            <span wire:loading wire:target="acceptMatch">
                                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                Принятие...
                                            </span>
                                        </button>
                                        <button class="btn btn-danger" wire:click="declineMatch" wire:loading.attr="disabled">
                                            <span wire:loading.remove wire:target="declineMatch"><i class="ri-close-line me-1"></i> Отклонить матч</span>
                                            <span wire:loading wire:target="declineMatch">
                                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                Отклонение...
                                            </span>
                                        </button>
                                        <button class="btn btn-warning" wire:click="findNewMatch" wire:loading.attr="disabled">
                                            <span class="text-white" wire:loading.remove wire:target="findNewMatch"><i class="ri-refresh-line me-1"></i> Найти другой матч</span>
                                            <span class="text-white" wire:loading wire:target="findNewMatch">
                                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                Поиск...
                                            </span>
                                        </button>
                                    @else
                                        <button class="btn btn-secondary" disabled>
                                            <i class="ri-check-double-line me-1"></i> Вы приняли матч
                                        </button>
                                        <button class="btn btn-danger" wire:click="declineMatch" wire:loading.attr="disabled">
                                            <span wire:loading.remove wire:target="declineMatch"><i class="ri-close-line me-1"></i> Отменить</span>
                                            <span wire:loading wire:target="declineMatch">
                                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                                Отмена...
                                            </span>
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                
                @elseif($searchStatus === 'error')
                    <div class="text-center py-4">
                        <i class="ri-error-warning-line fs-1 text-danger"></i>
                        <h5 class="mt-3">Произошла ошибка</h5>
                        <p class="text-muted">Пожалуйста, попробуйте еще раз.</p>
                        <button class="btn btn-primary btn-lg mt-3" wire:click="findNewMatch" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="findNewMatch"><i class="ri-refresh-line me-1"></i> Повторить поиск</span>
                            <span wire:loading wire:target="findNewMatch">
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                Повтор...
                            </span>
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>

    @if($searchStatus === 'map_voting')
        <div class="text-center py-4">
            <i class="ri-map-pin-line fs-1 text-warning"></i>
            <h5 class="mt-3">Бан карт</h5>
            
            <livewire:map-ban :match="$foundMatch" :team="$team" />
        </div>
    @endif

    <div>
        @if (session()->has('error'))
            <div class="alert alert-danger">
                {{ session('error') }}
            </div>
        @endif
    
        @if (session()->has('info'))
            <div class="alert alert-info">
                {{ session('info') }}
            </div>
        @endif
    
        <div class="mb-3">
            <small class="text-muted">Текущий статус: {{ $searchStatus }}</small>
        </div>
    </div>

    {{-- Модальное окно --}}
    <div x-cloak
      x-show="showAcceptModal" {{-- Теперь управляется привязанным свойством Livewire --}}
      x-transition:enter="transition ease-out duration-300"
      x-transition:enter-start="opacity-0"
      x-transition:enter-end="opacity-100"
      x-transition:leave="transition ease-in duration-200"
      x-transition:leave-start="opacity-100"
      x-transition:leave-end="opacity-0"
      class="modal" 
      :class="{ 'show': showAcceptModal }" 
      style="background-color: rgba(0,0,0,0.5);"
      tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Матч найден!</h5>
                    {{-- Закрытие модального окна через Livewire-метод --}}
                    {{-- Просто изменяем свойство Livewire, Alpine обновится сам --}}
                    <button type="button" class="btn-close" @click="$wire.showAcceptModal = false;"></button> 
                </div>
                <div class="modal-body text-center">
                    <i class="ri-trophy-line fs-1 text-success mb-3"></i>
                    <h5>Соперник принял матч!</h5>
                    <p>Команда соперника готова. Хотите продолжить и перейти к банам карт?</p>
                    {{-- Пример использования matchData: <p>ID Матча: <span x-text="matchData"></span></p> --}}
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button"
                            class="btn btn-success"
                            @click="$wire.acceptMatch(); $wire.showAcceptModal = false;"> {{-- Вызов Livewire-метода и закрытие --}}
                        <i class="ri-check-line me-1"></i> Принять и начать баны
                    </button>
                    <button type="button"
                            class="btn btn-danger"
                            @click="$wire.declineMatch(); $wire.showAcceptModal = false;"> {{-- Вызов Livewire-метода и закрытие --}}
                        <i class="ri-close-line me-1"></i> Отклонить
                    </button>
                </div>
            </div>
        </div>
    </div>
    <style>
        [x-cloak] { display: none !important; }
        .modal.show { display: block !important; } 
        .modal-backdrop { display: none !important; } 
    </style>
</div>

@push('scripts')
<script>
    document.addEventListener('livewire:init', () => {
        // Убедитесь, что Echo инициализирован до этого
        if (typeof Echo === 'undefined') {
            console.error('Echo (Laravel Echo) is not defined. Please ensure it is loaded correctly.');
            return;
        }

        Echo.private('captain.' + {{ auth()->id() }})
            .listen('.match.accepted', (e) => {
                console.log('Event data:', e);
                const currentUserId = parseInt({{ auth()->id() }});
                const toCaptainId = parseInt(e.toCaptainId);

                console.log('Current user ID:', currentUserId);
                console.log('Comparing:', toCaptainId, currentUserId);

                if (toCaptainId === currentUserId) {
                    console.log('Match is for current captain, dispatching Livewire event...');

                    // Livewire.dispatch теперь отправляет событие, которое будет прослушано в PHP
                    Livewire.dispatch('showMatchAcceptModal', { matchId: e.matchId });
                    
                    // Воспроизводим звук - это отдельное действие
                    const audio = new Audio('/sounds/zvonkoe-uvedomlenie-ob-sms.mp3');
                    audio.play().catch(err => console.log('Audio playback blocked by browser or other error:', err));
                } else {
                    console.log('Event is not for current captain');
                }
            });
         
        // Обработка событий map.banned теперь только в MapBan компоненте
    });


</script>
@endpush