<div>
    <div x-data="{ 
            open: false,
            init() {
                window.addEventListener('open-invite-player-modal', (e) => {
                    this.open = true;
                    this.teamIdForInvite = e.detail.teamId; // Получаем teamId
                    document.body.classList.add('modal-open');
                });
                window.addEventListener('closeInviteModal', () => {
                    this.open = false;
                    document.body.classList.remove('modal-open');
                });
                this.$watch('open', value => {
                    if (!value) {
                        document.body.classList.remove('modal-open');
                    }
                });
            }
        }"
        @keydown.escape.window="open = false"
        class="modal fade"
        :class="{ 'show': open }"
        style="display: none;"
        :style="open ? 'display: block;' : 'display: none;'"
        tabindex="-1"
        aria-labelledby="invitePlayerModalLabel"
        aria-hidden="true"
        wire:ignore.self>
        
        <div class="modal-backdrop fade" 
             :class="{ 'show': open }"
             x-show="open" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="open = false"></div>
             
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="invitePlayerModalLabel">Пригласить игрока</h5>
                    <button type="button" class="btn-close" @click="open = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit="sendInvitation">
                        <div class="mb-3">
                            @if(count($playerOptions) > 0)
                                <livewire:alpine-select 
                                    :options="$playerOptions" 
                                    :selected="$playerName" 
                                    name="playerName" 
                                    placeholder="Выберите игрока" 
                                    label="Имя игрока" 
                                    :required="true"
                                    itemValue="id"
                                    itemText="name"
                                />
                                @error('playerName') <span class="text-danger">{{ $message }}</span> @enderror
                            @else
                                <div class="alert alert-warning">
                                    Нет доступных игроков для приглашения
                                </div>
                            @endif
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" @click="open = false">Отмена</button>
                            <button type="submit" class="btn btn-primary" @if(count($playerOptions) == 0) disabled @endif>Отправить приглашение</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>