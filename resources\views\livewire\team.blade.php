<div>
    <div class="row">
        <div class="col-sm-10 offset-1">
            <div class="container py-4">
                @include('livewire.notification')
            </div>
            
            <div class="card">
                <div class="card-body">
                    <livewire:carousel />
                    
                    <!-- Индикатор загрузки -->
                    <div wire:loading wire:target="updateSelectedGame" class="text-center my-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Загрузка...</span>
                        </div>
                    </div>
                    
                    <div class="row" wire:loading.class.delay="opacity-50">
                        <div class="col-sm-3 raring_block">
                            <div class="position-relative text-center" style="cursor: pointer;" wire:click="gotoRating('club')">
                                <img src="{{ asset('images/top_club_2.png') }}" alt="">
                                <span class="position-absolute top-50 start-50 translate-middle fw-bold" style="z-index: 1;">{{ $seasonRating ?? 0 }}</span>
                            </div>
                        </div>
                        <div class="col-sm-6" x-data="{editUser: false, tooltip: null}">
                            <div class="position-relative pb-5">
                                <div class="d-flex align-items-center justify-content-center mb-4">
                                    <button type="button" class="btn btn-sm btn-outline-primary me-2 position-relative"
                                            wire:click='changeUser' 
                                            x-data="{showTooltip: false}"
                                            @mouseenter="showTooltip = true" 
                                            @mouseleave="showTooltip = false"
                                            @focus="showTooltip = true"
                                            @blur="showTooltip = false">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" width="20" height="20"><path d="M5.23379 7.72989C6.65303 5.48625 9.15342 4 12.0002 4C14.847 4 17.3474 5.48625 18.7667 7.72989L20.4569 6.66071C18.6865 3.86199 15.5612 2 12.0002 2C8.43928 2 5.31393 3.86199 3.54356 6.66071L5.23379 7.72989ZM12.0002 20C9.15342 20 6.65303 18.5138 5.23379 16.2701L3.54356 17.3393C5.31393 20.138 8.43928 22 12.0002 22C15.5612 22 18.6865 20.138 20.4569 17.3393L18.7667 16.2701C17.3474 18.5138 14.847 20 12.0002 20ZM12 12C13.6569 12 15 10.6569 15 9C15 7.34315 13.6569 6 12 6C10.3431 6 9 7.34315 9 9C9 10.6569 10.3431 12 12 12ZM12 13C14.2091 13 16 14.7909 16 17H8C8 14.7909 9.79086 13 12 13ZM6 12C6 13.6569 4.65685 15 3 15C1.34315 15 0 13.6569 0 12C0 10.3431 1.34315 9 3 9C4.65685 9 6 10.3431 6 12ZM21 15C22.6569 15 24 13.6569 24 12C24 10.3431 22.6569 9 21 9C19.3431 9 18 10.3431 18 12C18 13.6569 19.3431 15 21 15Z"></path></svg>
                                        <div x-show="showTooltip" class="tooltip-alpine" x-transition>Сменить пользователя</div>
                                    </button>
                                    
                                    <div x-show="!editUser">
                                        <h5 class="mb-0 mx-2" style="text-shadow: 1px 1px 13px #001414">{{ auth()->user()->client_nick }}</h5>
                                    </div>
                                    <div x-show="editUser">
                                        <input type="text" class="form-control" wire:model="client_nick" style="font-size: 1rem;">
                                        @error('client_nick') <div class="text-danger mt-1">{{ $message }}</div> @enderror
                                    </div>

                                    <button type="button" class="btn btn-sm btn-outline-secondary me-2 ms-2 position-relative"                                            
                                            @click="editUser = !editUser"
                                            x-data="{showTooltip: false}"
                                            @mouseenter="showTooltip = true" 
                                            @mouseleave="showTooltip = false"
                                            @focus="showTooltip = true"
                                            @blur="showTooltip = false">
                                        <i x-show="!editUser" class="ri-edit-line"></i>
                                        <i x-show="editUser" class="ri-save-line" wire:click='saveName'></i>
                                        <div x-show="showTooltip" class="tooltip-alpine" x-transition>Изменить имя</div>
                                    </button>
                                    
                                    <button type="button" class="btn btn-sm btn-outline-info position-relative"
                                            wire:click='createTam'
                                            x-data="{showTooltip: false}"
                                            @mouseenter="showTooltip = true" 
                                            @mouseleave="showTooltip = false"
                                            @focus="showTooltip = true"
                                            @blur="showTooltip = false">
                                        <i class="ri-team-line"></i>
                                        <div x-show="showTooltip" class="tooltip-alpine" x-transition>Команда</div>
                                    </button>
                                </div>                                
                            </div>
                            <div class="rating_line position-relative text-center my-5 pt-5">
                                <div class="position-relative mb-5 pb-5">
                                    <span class="position-absolute team-rating" style="left: 51%; transform: translateX(-50%);">{{ $teamRating }}</span>
                                </div>
                                <div class="position-relative pt-5" style="width: 80%; margin: 0 auto;">
                                    <div class="gradient-bar" style="height: 25px; border-radius: 12px; margin: 0 auto;"></div>
                                    <span id="rating_line" class="position-absolute translate-middle-y" style="top: 50%; left: {{ min(max((($teamRating + 10000) / 20000) * 100, 2), 98) }}%; transform: translateX(-50%);">|</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3 raring_block">
                            <div class="position-relative text-center" style="cursor: pointer;" wire:click="gotoRating('general')">
                                <img src="{{ asset('images/top_tournament_2.png') }}" alt="">
                                <span class="position-absolute top-50 start-50 translate-middle fw-bold" style="z-index: 1;">{{ $playerRank ?? 0 }}</span>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <h3>Последние матчи</h3>
                        @forelse($recentMatches as $match)
                            <div class="col-md-4">
                                <div class="card text-center h-100 {{ $match->matchResult && $match->matchResult->victory ? 'border-success' : 'border-danger' }}">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">
                                            @if($match instanceof \App\Models\GameMatch)
                                                CS2
                                            @elseif($match instanceof \App\Models\Dota2)
                                                Dota 2
                                            @elseif($match instanceof \App\Models\Pubg)
                                                PUBG
                                            @endif
                                        </h5>
                                        <span class="badge bg-primary">{{ $selectedMap }}</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-0">Счет: {{ $match->score }}</h6>
                                                <p class="mb-0 text-muted">Результат: 
                                                    @if($match->result === 'win')
                                                        <span class="text-success">Победа</span>
                                                    @elseif($match->result === 'lose')
                                                        <span class="text-danger">Поражение</span>
                                                    @else
                                                        <span class="text-warning">В процессе</span>
                                                    @endif
                                                </p>
                                            </div>
                                            <div class="text-end">
                                                <p class="mb-0">Рейтинг: {{ $match->rating }}</p>
                                                <small class="text-muted">{{ $match->date->format('d.m.Y H:i') }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="col-12">
                                <div class="alert alert-info">
                                    У вас пока нет сыгранных матчей
                                </div>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>















