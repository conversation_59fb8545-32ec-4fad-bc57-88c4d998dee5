    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Матчи команды</h5>
            <div class="d-flex gap-2">
                <select wire:model.live="resultFilter" class="form-select form-select-sm" style="width: auto;">
                    <option value="">Все результаты</option>
                    <option value="1">Победы</option>
                    <option value="0">Поражения</option>
                </select>
                <button type="button" class="btn btn-primary btn-sm" wire:click="$refresh">
                    <i class="ri-refresh-line"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            @if($matches->isEmpty())
                <div class="text-center text-muted py-4">
                    <i class="ri-game-line fs-1"></i>
                    <p class="mt-2">Нет доступных матчей</p>
                </div>
            @else
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Дата</th>
                                <th>Турнир</th>
                                <th>Соперник</th>
                                <th>Результат</th>
                                <th>Статус</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($matches as $match)
                                <tr>
                                    <td>{{ \Carbon\Carbon::parse($match->date)->format('d.m.Y H:i') }}</td>
                                    <td>
                                        <a href="{{ route('tournament.show', $match->tournament_id) }}" class="text-decoration-none">
                                            {{ $match->tournament_name }}
                                        </a>
                                    </td>
                                    <td>
                                        @if($match->opponent_team_id)
                                            <a href="{{ route('team.show', $match->opponent_team_id) }}" class="text-decoration-none">
                                                {{ $match->opponent_team_name }}
                                            </a>
                                        @else
                                            <span class="text-muted">Ожидается соперник</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($match->score)
                                            <span class="badge bg-{{ $match->victory ? 'success' : 'danger' }}">
                                                {{ $match->score }}
                                            </span>
                                        @else
                                            <span class="badge bg-secondary">Ожидается</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $match->status === 'completed' ? 'success' : ($match->status === 'scheduled' ? 'primary' : 'warning') }}">
                                            {{ $match->status === 'completed' ? 'Завершен' : ($match->status === 'scheduled' ? 'Запланирован' : 'В процессе') }}
                                        </span>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    </div> 