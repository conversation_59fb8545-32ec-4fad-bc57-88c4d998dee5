<!DOCTYPE html>
<html>
<head>
    <title>Simple Pusher Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header">
                <h3>Простой тест Pusher</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>Ключи Pusher:</strong><br>
                    App ID: 1978361<br>
                    Key: 550c02c2147f42012a1e<br>
                    Cluster: eu
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5>Статус:</h5>
                        <div id="status" class="badge bg-secondary">Инициализация...</div>
                        
                        <h5 class="mt-3">Лог:</h5>
                        <div id="log" class="border p-3" style="height: 300px; overflow-y: auto; background: #f8f9fa; font-family: monospace; font-size: 12px;"></div>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>Тест:</h5>
                        <button class="btn btn-primary" onclick="testConnection()">Тест подключения</button>
                        <button class="btn btn-success" onclick="sendTestMessage()">Отправить тест</button>
                        <button class="btn btn-warning" onclick="subscribeToTest()">Подписаться на тест</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {{-- <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    
    <script>
        let pusher;
        let channel;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(status, className = 'bg-secondary') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `badge ${className}`;
        }
        
        function initPusher() {
            try {
                log('Инициализируем Pusher...');
                
                // Включаем отладку
                Pusher.logToConsole = true;
                
                pusher = new Pusher('550c02c2147f42012a1e', {
                    cluster: 'eu',
                    forceTLS: true
                });
                
                log('Pusher создан');
                
                // Обработчики событий подключения
                pusher.connection.bind('connected', function() {
                    log('✅ Подключено к Pusher!');
                    updateStatus('Подключено', 'bg-success');
                });
                
                pusher.connection.bind('disconnected', function() {
                    log('❌ Отключено от Pusher');
                    updateStatus('Отключено', 'bg-warning');
                });
                
                pusher.connection.bind('error', function(err) {
                    log('🚨 Ошибка подключения: ' + JSON.stringify(err));
                    updateStatus('Ошибка', 'bg-danger');
                });
                
                pusher.connection.bind('state_change', function(states) {
                    log(`🔄 Смена состояния: ${states.previous} → ${states.current}`);
                    updateStatus(states.current);
                });
                
                log('Обработчики событий установлены');
                
            } catch (error) {
                log('🚨 Ошибка инициализации: ' + error.message);
                updateStatus('Ошибка инициализации', 'bg-danger');
            }
        }
        
        function testConnection() {
            if (!pusher) {
                log('Pusher не инициализирован');
                return;
            }
            
            log('Тестируем подключение...');
            log('Текущее состояние: ' + pusher.connection.state);
            updateStatus(pusher.connection.state);
        }
        
        function subscribeToTest() {
            if (!pusher) {
                log('Pusher не инициализирован');
                return;
            }
            
            log('Подписываемся на канал test-channel...');
            
            channel = pusher.subscribe('test-channel');
            
            channel.bind('pusher:subscription_succeeded', function() {
                log('✅ Подписка на канал успешна');
            });
            
            channel.bind('pusher:subscription_error', function(err) {
                log('🚨 Ошибка подписки: ' + JSON.stringify(err));
            });
            
            channel.bind('test-event', function(data) {
                log('📨 Получено событие: ' + JSON.stringify(data));
            });
        }
        
        function sendTestMessage() {
            // Отправляем тестовое событие через Laravel
            fetch('/test-simple-pusher', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    message: 'Тестовое сообщение',
                    timestamp: new Date().toISOString()
                })
            })
            .then(response => response.json())
            .then(data => {
                log('📤 Тестовое событие отправлено: ' + JSON.stringify(data));
            })
            .catch(error => {
                log('🚨 Ошибка отправки: ' + error.message);
            });
        }
        
        // Автоматически инициализируем при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Страница загружена');
            initPusher();
            
            // Автоматически тестируем подключение через 2 секунды
            setTimeout(testConnection, 2000);
        });
    </script> --}}
</body>
</html>
