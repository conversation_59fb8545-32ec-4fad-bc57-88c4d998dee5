<div>
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h3>Бан карт для матча #<?php echo e($match->id); ?></h3>
            <p class="text-muted">Выберите до <?php echo e($maxBans); ?> карт для бана</p>

            <!--[if BLOCK]><![endif]--><?php if($currentVotingTeam): ?>
                <div class="alert alert-info">
                    <!--[if BLOCK]><![endif]--><?php if($currentVotingTeam->id === $teamId): ?>
                        <strong>Ваш ход!</strong> Выберите карту для бана.
                    <?php else: ?>
                        <strong>Ход команды "<?php echo e($currentVotingTeam->name); ?>"</strong> - ожидайте...
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <div class="mb-3">
                <small class="text-muted">
                    Забанено карт: <?php echo e(count($bannedMaps)); ?> / <?php echo e($maxBans); ?>

                </small>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Доступные карты</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableMaps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $map): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!--[if BLOCK]><![endif]--><?php if(!in_array($map->id, $bannedMaps)): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <!--[if BLOCK]><![endif]--><?php if($map->image_url): ?>
                                            <img src="<?php echo e(asset($map->image_url)); ?>" 
                                                 class="card-img-top" 
                                                 alt="<?php echo e($map->name); ?>">
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <div class="card-body">
                                            <h6 class="card-title"><?php echo e($map->name); ?></h6>
                                            <button class="btn btn-danger btn-sm w-100"
                                                    wire:click="banMap(<?php echo e($map->id); ?>)"
                                                    wire:loading.attr="disabled"
                                                    <?php echo e($currentVotingTeam && $currentVotingTeam->id !== $teamId ? 'disabled' : ''); ?>>
                                                <span wire:loading.remove wire:target="banMap(<?php echo e($map->id); ?>)">
                                                    Забанить
                                                </span>
                                                <span wire:loading wire:target="banMap(<?php echo e($map->id); ?>)">
                                                    <span class="spinner-border spinner-border-sm"></span>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Забаненные карты</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $availableMaps; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $map): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!--[if BLOCK]><![endif]--><?php if(in_array($map->id, $bannedMaps)): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card h-100">
                                        <!--[if BLOCK]><![endif]--><?php if($map->image_url): ?>
                                            <img src="<?php echo e(asset('maps/' . $map->image_url)); ?>" 
                                                 class="card-img-top" 
                                                 alt="<?php echo e($map->name); ?>">
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <div class="card-body">
                                            <h6 class="card-title"><?php echo e($map->name); ?></h6>
                                            <button class="btn btn-success btn-sm w-100" 
                                                    wire:click="unbanMap(<?php echo e($map->id); ?>)"
                                                    wire:loading.attr="disabled">
                                                <span wire:loading.remove wire:target="unbanMap(<?php echo e($map->id); ?>)">
                                                    Разбанить
                                                </span>
                                                <span wire:loading wire:target="unbanMap(<?php echo e($map->id); ?>)">
                                                    <span class="spinner-border spinner-border-sm"></span>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


    <div class="card">
        <div class="card-header bg-light">
            <h6 class="mb-0">Выбор карт</h6>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6 text-center">
                    <h6>Ваша команда</h6>
                    <h5><?php echo e($team->name); ?></h5>
                </div>
                <div class="col-md-6 text-center">
                    <h6>Команда соперников</h5>
                    <h5><?php echo e($opponents->name ?? 'Неизвестная команда'); ?></h5>
                </div>
            </div>
            
            <!-- Остальная часть шаблона остается без изменений -->
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('livewire:init', () => {
        Echo.private('captain.' + <?php echo e(auth()->id()); ?>)
            .listen('.map.banned', (e) => {
                console.log('Map banned event:', e);

                if (parseInt(e.toCaptainId) === parseInt(<?php echo e(auth()->id()); ?>)) {
                    Livewire.dispatch('map.banned', {
                        matchId: e.matchId,
                        mapId: e.mapId,
                        fromCaptainId: e.fromCaptainId,
                        toCaptainId: e.toCaptainId,
                        nextVotingTeamId: e.nextVotingTeamId
                    });

                    const audio = new Audio('/sounds/notification.mp3');
                    audio.play().catch(err => console.log('Audio error:', err));
                }
            });

        // Обработка завершения голосования по картам
        Livewire.on('mapVotingCompleted', (event) => {
            console.log('Map voting completed:', event);

            // Показываем уведомление о завершении
            const audio = new Audio('/sounds/zvonkoe-uvedomlenie-ob-sms.mp3');
            audio.play().catch(err => console.log('Audio error:', err));

            // Можно добавить перенаправление или другие действия
            setTimeout(() => {
                // window.location.href = '/match/' + event.matchId;
            }, 3000);
        });
    });
</script>
<?php $__env->stopPush(); ?><?php /**PATH C:\OSPanel\domains\web-rgtournament2\resources\views/livewire/map-ban.blade.php ENDPATH**/ ?>