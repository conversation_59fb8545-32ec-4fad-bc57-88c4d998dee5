<!DOCTYPE html>
<html
  lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>"
  class="dark-style layout-menu-fixed layout-compact"
  dir="ltr"
  data-theme="theme-default"
  data-assets-path="<?php echo e(asset('assets/')); ?>"
  data-template="vertical-menu-template-free"
  data-style="dark">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="user-id" content="<?php echo e(Auth::id()); ?>">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e($title ?? 'Page Title'); ?></title>
        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="<?php echo e(asset('assets/img/favicon/favicon.ico')); ?>" />

        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&ampdisplay=swap"
          rel="stylesheet" /> 

        <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/fonts/remixicon/remixicon.css')); ?>" />

        <!-- Core CSS -->
        <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/css/core.css')); ?>" class="template-customizer-core-css" />
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/style.css')); ?>" />
        <!-- Дополнительные переопределения для темной темы -->
        <link rel="stylesheet" href="<?php echo e(asset('assets/css/dark-override.css')); ?>" />
        <?php echo $__env->yieldPushContent('styles'); ?>
        
        <!-- Vite Assets -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    </head>
    <body>
        <main class="container">

            <nav class="navbar navbar-expand-lg mb-1 bg-body-tertiary">
                <div class="container-fluid">
                  <a class="navbar-brand" href="<?php echo e(route('home')); ?>" wire:navigate>RGTournament</a>
                  <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                  </button>
                  <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                      <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('team') ? 'active' : ''); ?>" <?php echo e(request()->routeIs('team') ? 'aria-current="page"' : ''); ?> href="<?php echo e(route('team')); ?>" wire:navigate>Team</a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('matches') ? 'active' : ''); ?>" <?php echo e(request()->routeIs('matches') ? 'aria-current="page"' : ''); ?> href="<?php echo e(route('matches')); ?>" wire:navigate>Matches</a>
                      </li>
                      <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('game-matches') ? 'active' : ''); ?>" <?php echo e(request()->routeIs('game-matches') ? 'aria-current="page"' : ''); ?> href="<?php echo e(route('game-matches')); ?>" wire:navigate>Game Matches</a>
                      </li>
                    </ul>
                    <ul class="d-flex navbar-nav">
                        <?php if(auth()->guard()->check()): ?>
                          <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user-menu', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2821800694-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?> 
                        <?php else: ?>
                            <li class="nav-item"><a class="nav-link" href="<?php echo e(route('login')); ?>" wire:navigate>Войти</a></li>
                            <li class="nav-item"><a class="nav-link" href="<?php echo e(route('register')); ?>" wire:navigate>Регистрация</a></li>
                        <?php endif; ?>
                    </ul>
                  </div>
                </div>
              </nav>

        
              <?php echo e($slot); ?>


        </main>

    <!-- Core JS -->
    <script src="<?php echo e(asset('assets/vendor/js/bootstrap.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/js/menu.js')); ?>"></script>

    <!-- Main JS -->
    <script src="<?php echo e(asset('assets/js/main.js')); ?>"></script>

    <!-- Pusher Debug Info -->
    

    <?php echo $__env->yieldPushContent('scripts'); ?>
    <?php echo $__env->yieldPushContent('carousel-scripts'); ?>

    <!-- Принудительное применение темной темы -->
    <script src="<?php echo e(asset('assets/js/force-dark-theme.js')); ?>"></script>
    
    <!-- Carousel JS -->
    <script src="<?php echo e(asset('assets/js/carousel.js')); ?>"></script>

    
    </body>
</html>
<?php /**PATH C:\OSPanel\domains\web-rgtournament2\resources\views/components/layouts/app.blade.php ENDPATH**/ ?>