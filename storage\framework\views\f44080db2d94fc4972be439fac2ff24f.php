<div>
    <!-- Контейнер для push-уведомлений -->
    <div id="notification-container" class="position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
    <!-- Включаем шаблон с уведомлениями напрямую -->
    <?php echo $__env->make('livewire.notification', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <h5 class="mb-0 me-3">Поиск матча</h5>
            </div>
            <button class="btn btn-sm btn-outline-primary" wire:click="backToTeam">
                <i class="ri-arrow-left-line me-1"></i> Вернуться к команде
            </button>
        </div>
        <div class="card-body">
            <!--[if BLOCK]><![endif]--><?php if($searchStatus === 'error'): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php else: ?>
                <div class="row">
                    <div class="col-md-12">
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('match-search', ['teamId' => $teamId, 'team' => $team, 'searchStatus' => $searchStatus, 'foundMatch' => $foundMatch]);

$__html = app('livewire')->mount($__name, $__params, 'lw-1363514667-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </div>
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">Ваша команда</h6>
                            </div>
                            <div class="card-body">
                                <h5><?php echo e($team->name); ?></h5>
                                <p class="text-muted"><?php echo e($team->description); ?></p>
                                
                                <!--[if BLOCK]><![endif]--><?php if($team && $team->members && count($team->members) > 0): ?>
                                <div class="mt-3">
                                    <h6>Состав команды:</h6>
                                    <ul class="list-group">
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $team->members; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <!--[if BLOCK]><![endif]--><?php if(isset($member->user->avatar) && $member->user->avatar): ?>
                                                        <img src="<?php echo e(asset($member->user->avatar)); ?>" class="rounded-circle me-2" width="30" height="30" alt="<?php echo e($member->user->client_nick ?? 'User'); ?>">
                                                    <?php else: ?>
                                                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px;">
                                                            <?php echo e(substr($member->user->client_nick ?? 'U', 0, 1)); ?>

                                                        </div>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    <?php echo e($member->user->client_nick ?? 'Unknown User'); ?>

                                                </div>
                                                <span class="badge bg-primary rounded-pill">
                                                    <?php echo e(($member->role ?? 'member') === 'captain' ? 'Капитан' : 'Игрок'); ?>

                                                </span>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </ul>
                                </div>
                                <?php else: ?>
                                <div class="mt-3">
                                    <div class="alert alert-warning">
                                        <i class="ri-information-line me-1"></i>
                                        Информация о составе команды недоступна
                                    </div>
                                </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>                    
                    
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>
<?php /**PATH C:\OSPanel\domains\web-rgtournament2\resources\views/livewire/find-match.blade.php ENDPATH**/ ?>