<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\LiveMatch;
use App\Models\Team;
use App\Models\User;
use App\Models\TeamMember;
use App\Models\Cs2Map;
use App\Models\LiveMatchBannedMap;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Foundation\Testing\WithFaker;

class MapBanTest extends TestCase
{
    use WithFaker;

    protected $team1;
    protected $team2;
    protected $captain1;
    protected $captain2;
    protected $match;

    /** @test */
    public function test_map_ban_component_loads()
    {
        // Простой тест загрузки компонента
        $this->assertTrue(class_exists(\App\Livewire\MapBan::class));
        $this->assertTrue(class_exists(\App\Models\LiveMatch::class));
        $this->assertTrue(class_exists(\App\Models\Cs2Map::class));
        $this->assertTrue(class_exists(\App\Models\LiveMatchBannedMap::class));
    }

    /** @test */
    public function test_enums_exist()
    {
        // Проверяем, что енумы существуют и имеют правильные значения
        $this->assertEquals('map_voting', MatchStatus::MAP_VOTING->value);
        $this->assertEquals('ready_check', MatchStatus::READY_CHECK->value);
        $this->assertEquals('live', MatchStatus::LIVE->value);

        $this->assertEquals('team1', CurrentVoter::TEAM1->value);
        $this->assertEquals('team2', CurrentVoter::TEAM2->value);
    }

    /** @test */
    public function test_livewire_component_exists()
    {
        // Проверяем, что Livewire компонент существует
        $this->assertTrue(class_exists(\App\Livewire\MapBan::class));

        // Проверяем, что у компонента есть нужные методы
        $reflection = new \ReflectionClass(\App\Livewire\MapBan::class);
        $this->assertTrue($reflection->hasMethod('banMap'));
        $this->assertTrue($reflection->hasMethod('handleMapBanned'));
        $this->assertTrue($reflection->hasMethod('render'));
    }

    /** @test */
    public function test_events_exist()
    {
        // Проверяем, что события существуют
        $this->assertTrue(class_exists(\App\Events\MapBanned::class));

        // Проверяем, что событие имеет нужные свойства
        $reflection = new \ReflectionClass(\App\Events\MapBanned::class);
        $constructor = $reflection->getConstructor();
        $parameters = $constructor->getParameters();

        $paramNames = array_map(fn($param) => $param->getName(), $parameters);
        $this->assertContains('matchId', $paramNames);
        $this->assertContains('mapId', $paramNames);
        $this->assertContains('fromCaptainId', $paramNames);
        $this->assertContains('toCaptainId', $paramNames);
        $this->assertContains('nextVotingTeamId', $paramNames);
    }

    /** @test */
    public function test_model_relationships()
    {
        // Проверяем, что модели имеют нужные связи
        $liveMatchReflection = new \ReflectionClass(\App\Models\LiveMatch::class);
        $this->assertTrue($liveMatchReflection->hasMethod('bannedMaps'));
        $this->assertTrue($liveMatchReflection->hasMethod('team1'));
        $this->assertTrue($liveMatchReflection->hasMethod('team2'));

        $bannedMapReflection = new \ReflectionClass(\App\Models\LiveMatchBannedMap::class);
        $this->assertTrue($bannedMapReflection->hasMethod('match'));
        $this->assertTrue($bannedMapReflection->hasMethod('map'));
        $this->assertTrue($bannedMapReflection->hasMethod('team'));
    }

    /** @test */
    public function test_blade_template_exists()
    {
        // Проверяем, что шаблон существует
        $templatePath = resource_path('views/livewire/map-ban.blade.php');
        $this->assertFileExists($templatePath);

        // Проверяем, что шаблон содержит нужные элементы
        $content = file_get_contents($templatePath);
        $this->assertStringContainsString('wire:click="banMap', $content);
        $this->assertStringContainsString('currentVotingTeam', $content);
        $this->assertStringContainsString('bannedMaps', $content);
    }
}
