<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\LiveMatch;
use App\Models\Team;
use App\Models\User;
use App\Models\TeamMember;
use App\Models\Cs2Map;
use App\Models\LiveMatchBannedMap;
use App\Enums\MatchStatus;
use App\Enums\CurrentVoter;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class MapBanTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $team1;
    protected $team2;
    protected $captain1;
    protected $captain2;
    protected $match;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем пользователей-капитанов
        $this->captain1 = User::factory()->create();
        $this->captain2 = User::factory()->create();
        
        // Создаем команды
        $this->team1 = Team::factory()->create(['name' => 'Team Alpha', 'rating' => 1500]);
        $this->team2 = Team::factory()->create(['name' => 'Team Beta', 'rating' => 1400]);
        
        // Создаем участников команд
        TeamMember::create([
            'team_id' => $this->team1->id,
            'player_id' => $this->captain1->id,
            'role' => 'captain'
        ]);
        
        TeamMember::create([
            'team_id' => $this->team2->id,
            'player_id' => $this->captain2->id,
            'role' => 'captain'
        ]);
        
        // Создаем карты CS2
        $maps = ['Dust2', 'Mirage', 'Inferno', 'Cache', 'Overpass', 'Vertigo', 'Ancient'];
        foreach ($maps as $mapName) {
            Cs2Map::create([
                'name' => $mapName,
                'is_active' => true,
                'image_url' => strtolower($mapName) . '.jpg'
            ]);
        }
        
        // Создаем матч
        $this->match = LiveMatch::create([
            'team1_id' => $this->team1->id,
            'team2_id' => $this->team2->id,
            'status' => MatchStatus::MAP_VOTING->value,
            'current_voter' => CurrentVoter::TEAM1->value,
            'current_voting_team' => $this->team1->id
        ]);
    }

    /** @test */
    public function test_match_creation()
    {
        $this->assertDatabaseHas('live_match', [
            'team1_id' => $this->team1->id,
            'team2_id' => $this->team2->id,
            'status' => MatchStatus::MAP_VOTING->value
        ]);
    }

    /** @test */
    public function test_map_ban_functionality()
    {
        $map = Cs2Map::first();
        
        // Тестируем бан карты
        $result = $this->match->banMap($map->id, $this->team1->id);
        
        $this->assertTrue($result);
        $this->assertDatabaseHas('live_match_banned_maps', [
            'match_id' => $this->match->id,
            'map_id' => $map->id,
            'team_id' => $this->team1->id
        ]);
        
        // Проверяем, что ход переключился
        $this->match->refresh();
        $this->assertEquals($this->team2->id, $this->match->current_voting_team);
    }

    /** @test */
    public function test_cannot_ban_same_map_twice()
    {
        $map = Cs2Map::first();
        
        // Банируем карту первый раз
        $this->match->banMap($map->id, $this->team1->id);
        
        // Пытаемся забанить ту же карту второй раз
        $result = $this->match->banMap($map->id, $this->team2->id);
        
        $this->assertFalse($result);
        
        // Проверяем, что в базе только одна запись
        $this->assertEquals(1, LiveMatchBannedMap::where('map_id', $map->id)->count());
    }

    /** @test */
    public function test_final_map_selection()
    {
        $maps = Cs2Map::all();
        
        // Банируем все карты кроме одной
        foreach ($maps->take($maps->count() - 1) as $map) {
            $this->match->banMap($map->id);
        }
        
        // Выбираем финальную карту
        $finalMap = $this->match->selectFinalMap();
        
        $this->assertNotNull($finalMap);
        $this->assertEquals(MatchStatus::LIVE->value, $this->match->fresh()->status);
        $this->assertEquals($finalMap->id, $this->match->fresh()->selected_map_id);
    }

    /** @test */
    public function test_get_current_voting_team()
    {
        $currentTeam = $this->match->getCurrentVotingTeam();
        
        $this->assertNotNull($currentTeam);
        $this->assertEquals($this->team1->id, $currentTeam->id);
    }

    /** @test */
    public function test_switch_voter()
    {
        $this->assertEquals($this->team1->id, $this->match->current_voting_team);
        
        $this->match->switchVoter();
        
        $this->assertEquals($this->team2->id, $this->match->current_voting_team);
        $this->assertEquals(CurrentVoter::TEAM2->value, $this->match->current_voter);
    }
}
